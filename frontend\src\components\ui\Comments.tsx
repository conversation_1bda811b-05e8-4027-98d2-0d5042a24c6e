'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { StarRating, DisplayStarRating } from './StarRating';
import { apiClient } from '@/lib/api';
import toast from 'react-hot-toast';
import {
  UserIcon,
  PencilIcon,
  TrashIcon,
  ChatBubbleLeftIcon
} from '@heroicons/react/24/outline';

interface Comment {
  _id: string;
  user: {
    _id: string;
    name: string;
    avatar?: string;
  };
  rating: number;
  comment: string;
  createdAt: string;
  updatedAt: string;
}

interface CommentsProps {
  entityType: 'city' | 'place';
  entityId: string;
  entitySlug: string;
}

export function Comments({ entityType, entityId, entitySlug }: CommentsProps) {
  const { user, isAuthenticated } = useAuth();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  
  // Form state
  const [rating, setRating] = useState(5);
  const [comment, setComment] = useState('');
  const [editRating, setEditRating] = useState(5);
  const [editComment, setEditComment] = useState('');

  useEffect(() => {
    fetchComments();
  }, [entityId]);

  const fetchComments = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getReviews(entityType, entitySlug);
      if (response.success && response.data) {
        setComments(response.data);
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isAuthenticated) {
      toast.error('Please login to leave a review');
      return;
    }

    if (!comment.trim()) {
      toast.error('Please write a comment');
      return;
    }

    try {
      setSubmitting(true);
      const response = await apiClient.addReview(entityType, entitySlug, {
        rating,
        comment: comment.trim()
      });

      if (response.success) {
        toast.success('Review added successfully!');
        setComment('');
        setRating(5);
        fetchComments(); // Refresh comments
      }
    } catch (error: any) {
      console.error('Error adding review:', error);
      toast.error(error.message || 'Failed to add review');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (commentData: Comment) => {
    setEditingId(commentData._id);
    setEditRating(commentData.rating);
    setEditComment(commentData.comment);
  };

  const handleUpdate = async (commentId: string) => {
    if (!editComment.trim()) {
      toast.error('Please write a comment');
      return;
    }

    try {
      const response = await apiClient.updateReview(entityType, entitySlug, commentId, {
        rating: editRating,
        comment: editComment.trim()
      });

      if (response.success) {
        toast.success('Review updated successfully!');
        setEditingId(null);
        fetchComments(); // Refresh comments
      }
    } catch (error: any) {
      console.error('Error updating review:', error);
      toast.error(error.message || 'Failed to update review');
    }
  };

  const handleDelete = async (commentId: string) => {
    if (!confirm('Are you sure you want to delete this review?')) {
      return;
    }

    try {
      const response = await apiClient.deleteReview(entityType, entitySlug, commentId);
      if (response.success) {
        toast.success('Review deleted successfully!');
        fetchComments(); // Refresh comments
      }
    } catch (error: any) {
      console.error('Error deleting review:', error);
      toast.error(error.message || 'Failed to delete review');
    }
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditRating(5);
    setEditComment('');
  };

  const userHasReviewed = comments.some(c => c.user._id === user?.id);
  const averageRating = comments.length > 0 
    ? comments.reduce((sum, c) => sum + c.rating, 0) / comments.length 
    : 0;

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <h3 className="text-xl font-semibold text-gray-900">Reviews & Ratings</h3>
          <div className="flex items-center space-x-2">
            <DisplayStarRating rating={averageRating} size="md" showValue={false} />
            <span className="text-lg font-medium text-gray-900">
              {averageRating.toFixed(1)}
            </span>
            <span className="text-sm text-gray-500">
              ({comments.length} {comments.length === 1 ? 'review' : 'reviews'})
            </span>
          </div>
        </div>
        <ChatBubbleLeftIcon className="h-6 w-6 text-gray-400" />
      </div>

      {/* Add Review Form */}
      {isAuthenticated && !userHasReviewed && (
        <form onSubmit={handleSubmit} className="mb-8 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Write a Review</h4>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Your Rating
            </label>
            <StarRating 
              rating={rating}
              onRatingChange={setRating}
              size="lg"
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Your Review
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Share your experience..."
              required
            />
          </div>

          <button
            type="submit"
            disabled={submitting}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {submitting ? 'Submitting...' : 'Submit Review'}
          </button>
        </form>
      )}

      {/* Login Prompt */}
      {!isAuthenticated && (
        <div className="mb-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <p className="text-blue-800">
            <a href="/auth/login" className="font-medium hover:underline">
              Login
            </a> to leave a review and rating.
          </p>
        </div>
      )}

      {/* Already Reviewed Message */}
      {isAuthenticated && userHasReviewed && (
        <div className="mb-8 p-4 bg-green-50 rounded-lg border border-green-200">
          <p className="text-green-800">
            You have already reviewed this {entityType}. You can edit or delete your review below.
          </p>
        </div>
      )}

      {/* Comments List */}
      {loading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="flex space-x-4">
                <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : comments.length > 0 ? (
        <div className="space-y-6">
          {comments.map((commentData) => (
            <div key={commentData._id} className="border-b border-gray-100 pb-6 last:border-b-0">
              {editingId === commentData._id ? (
                // Edit Form
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <UserIcon className="h-6 w-6 text-gray-500" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{commentData.user.name}</p>
                      <p className="text-sm text-gray-500">Editing review</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Rating
                    </label>
                    <StarRating 
                      rating={editRating}
                      onRatingChange={setEditRating}
                      size="md"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Review
                    </label>
                    <textarea
                      value={editComment}
                      onChange={(e) => setEditComment(e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={() => handleUpdate(commentData._id)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Update
                    </button>
                    <button
                      onClick={cancelEdit}
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                // Display Comment
                <div>
                  <div className="flex items-start justify-between">
                    <div className="flex space-x-4">
                      <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <UserIcon className="h-6 w-6 text-gray-500" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <p className="font-medium text-gray-900">{commentData.user.name}</p>
                          <DisplayStarRating rating={commentData.rating} size="sm" showValue={false} />
                          <span className="text-sm text-gray-500">
                            {new Date(commentData.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-gray-700">{commentData.comment}</p>
                      </div>
                    </div>

                    {/* Edit/Delete buttons for own comments */}
                    {user?.id === commentData.user._id && (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(commentData)}
                          className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          title="Edit review"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(commentData._id)}
                          className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          title="Delete review"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <ChatBubbleLeftIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-600">No reviews yet. Be the first to share your experience!</p>
        </div>
      )}
    </div>
  );
}
