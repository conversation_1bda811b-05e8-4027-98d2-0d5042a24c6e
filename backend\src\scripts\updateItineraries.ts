import mongoose from 'mongoose';
import { Itinerary } from '../models/Itinerary';

const updateItineraries = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/heritedge');
    console.log('Connected to MongoDB');

    // Update all itineraries to be public and published
    const result = await Itinerary.updateMany(
      {}, // Update all itineraries
      {
        $set: {
          isPublic: true,
          status: 'published'
        }
      }
    );

    console.log(`Updated ${result.modifiedCount} itineraries to be public and published`);

    // Get all itineraries to verify
    const itineraries = await Itinerary.find({})
      .populate<{ city: { name: string; country: string }, user: { name: string } }>('city', 'name country')
      .populate('user', 'name')
      .select('title city user isPublic status createdAt');

    console.log('\nCurrent itineraries:');
    itineraries.forEach((itinerary, index) => {
      console.log(`${index + 1}. ${itinerary.title}`);
      // Type assertion to help TypeScript recognize populated fields
      const city = itinerary.city as { name: string; country: string };
      const user = itinerary.user as { name: string };
      console.log(`   City: ${city.name}, ${city.country}`);
      console.log(`   User: ${user.name}`);
      console.log(`   Status: ${itinerary.status}, Public: ${itinerary.isPublic}`);
      console.log(`   Created: ${itinerary.createdAt}`);
      console.log('');
    });

    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error updating itineraries:', error);
    process.exit(1);
  }
};

updateItineraries();
