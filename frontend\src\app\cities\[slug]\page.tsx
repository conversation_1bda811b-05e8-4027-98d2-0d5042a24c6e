'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  MapPinIcon,
  UsersIcon,
  GlobeAltIcon,
  ClockIcon,
  CurrencyDollarIcon,
  LanguageIcon,
  SunIcon,
  HeartIcon,
  ShareIcon,
  PaperAirplaneIcon,
  BuildingOfficeIcon,
  TruckIcon,
  BuildingLibraryIcon,
  HomeModernIcon,
  ShoppingBagIcon,
  MusicalNoteIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import { apiClient } from '@/lib/api';
import { City, Place } from '@/types';
import { CityBreadcrumb } from '@/components/ui/Breadcrumb';
import { getImageUrl } from '@/lib/utils';
import { Comments } from '@/components/ui/Comments';
import { DisplayStarRating } from '@/components/ui/StarRating';

export default function CityPage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [city, setCity] = useState<City | null>(null);
  const [places, setPlaces] = useState<Place[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'places' | 'info' | 'reviews'>('overview');

  // Fetch city data
  const fetchCity = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getCity(slug);
      if (response.success && response.data && response.data.city) {
        setCity(response.data.city);
        // Fetch places for this city
        const placesResponse = await apiClient.getCityPlaces(slug);
        if (placesResponse.success && placesResponse.data) {
          setPlaces(placesResponse.data.places || []);
        }
      } else {
        setError('City not found');
      }
    } catch (err) {
      console.error('Error fetching city:', err);
      setError('Failed to load city data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (slug) {
      fetchCity();
    }
  }, [slug]);

  // No mock data - use only real database data

  // No mock places data - use only real database data

  const handleBookmark = async () => {
    try {
      if (isBookmarked) {
        await apiClient.unbookmarkCity(slug);
      } else {
        await apiClient.bookmarkCity(slug);
      }
      setIsBookmarked(!isBookmarked);
    } catch (err) {
      console.error('Error toggling bookmark:', err);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: city?.name,
          text: city?.description,
          url: window.location.href,
        });
      } catch (err) {
        console.error('Error sharing:', err);
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="animate-pulse">
          <div className="h-96 bg-gray-200"></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
              <div>
                <div className="h-64 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !city) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <GlobeAltIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">City not found</h1>
          <p className="text-gray-600 mb-6">The city you're looking for doesn't exist or has been removed.</p>
          <Link
            href="/cities"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Browse All Cities
          </Link>
        </div>
      </div>
    );
  }

  const primaryImage = city.images?.find(img => img.isPrimary) || city.images?.[0];
  const heroImageUrl = primaryImage ? getImageUrl(primaryImage.url) : '/assets/placeholders/city-placeholder.jpg';

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="relative h-96 overflow-hidden">
        <img
          src={heroImageUrl}
          alt={primaryImage?.alt || `${city.name} cityscape`}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="absolute inset-0 flex items-end">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8 w-full">
            <div className="text-white">
              <div className="flex items-center space-x-2 mb-2">
                <MapPinIcon className="h-5 w-5" />
                <span className="text-lg">{city.country}</span>
                {city.state && <span className="text-lg">• {city.state}</span>}
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-4">{city.name}</h1>
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <DisplayStarRating rating={city.averageRating || 0} size="md" showValue={false} />
                  <span className="text-lg font-medium">{(city.averageRating || 0).toFixed(1)}</span>
                  <span className="text-blue-200">({city.totalReviews?.toLocaleString() || 0} reviews)</span>
                </div>
                {city.population && (
                  <div className="flex items-center space-x-1">
                    <UsersIcon className="h-5 w-5" />
                    <span className="text-lg">{((city.population || 0) / 1000000).toFixed(1)}M people</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="absolute top-4 right-4 flex space-x-2">
          <button
            onClick={handleBookmark}
            className="p-3 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors"
          >
            {isBookmarked ? (
              <HeartIconSolid className="h-6 w-6 text-red-500" />
            ) : (
              <HeartIcon className="h-6 w-6 text-gray-700" />
            )}
          </button>
          <button
            onClick={handleShare}
            className="p-3 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors"
          >
            <ShareIcon className="h-6 w-6 text-gray-700" />
          </button>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <CityBreadcrumb
            cityName={city.name}
            citySlug={city.slug}
            section={
              activeTab === 'overview' || activeTab === 'places'
                ? activeTab
                : undefined
            }
          />
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', name: 'Overview' },
              { id: 'places', name: `Places (${places.length})` },
              { id: 'info', name: 'Travel Info' },
              { id: 'reviews', name: 'Reviews' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">About {city.name}</h2>
                  <p className="text-gray-700 text-lg leading-relaxed">{city.overview}</p>
                </div>

                {/* Tags */}
                {city.tags && city.tags.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">What makes it special</h3>
                    <div className="flex flex-wrap gap-2">
                      {city.tags.map((tag) => (
                        <span
                          key={tag}
                          className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Culture */}
                {city.culture && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Culture & Traditions</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {city.culture.festivals && city.culture.festivals.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Festivals</h4>
                          <ul className="text-sm text-gray-600 space-y-1">
                            {city.culture.festivals.map((festival, index) => (
                              <li key={index}>• {festival}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {city.culture.traditions && city.culture.traditions.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Traditions</h4>
                          <ul className="text-sm text-gray-600 space-y-1">
                            {city.culture.traditions.map((tradition, index) => (
                              <li key={index}>• {tradition}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {city.culture.artAndCrafts && city.culture.artAndCrafts.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Arts & Crafts</h4>
                          <ul className="text-sm text-gray-600 space-y-1">
                            {city.culture.artAndCrafts.map((art, index) => (
                              <li key={index}>• {art}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'places' && (
              <div className="space-y-8">
                <div className="flex justify-between items-center">
                  <h2 className="text-2xl font-bold text-gray-900">Top Places to Visit</h2>
                  {places.length > 0 && (
                    <div className="text-sm text-gray-600">
                      {places.length} place{places.length !== 1 ? 's' : ''} found
                    </div>
                  )}
                </div>

                {places.length === 0 ? (
                  <div className="text-center py-12">
                    <MapPinIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-lg font-medium text-gray-900 mb-2">No places found</p>
                    <p className="text-gray-600">Places for this city will be added soon.</p>
                  </div>
                ) : (
                  <div className="space-y-8">
                    {/* Group places by category */}
                    {Object.entries(groupPlacesByCategory(places)).map(([category, categoryPlaces]) => (
                      <div key={category} className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <CategoryIcon category={category} className="h-5 w-5 text-blue-600" />
                          <h3 className="text-lg font-semibold text-gray-900 capitalize">
                            {category === 'attraction' ? 'Attractions' :
                             category === 'restaurant' ? 'Restaurants' :
                             category === 'museum' ? 'Museums' :
                             category === 'religious' ? 'Religious Sites' :
                             category === 'historical' ? 'Historical Sites' :
                             category}
                          </h3>
                          <span className="text-sm text-gray-500">({categoryPlaces.length})</span>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
                          {categoryPlaces.map((place) => (
                            <PlaceCard key={place.id} place={place} />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'info' && (
              <div className="space-y-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Travel Information</h2>
                
                {/* Climate */}
                {city.climate && (
                  <div className="bg-white rounded-xl p-6 shadow-sm">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <SunIcon className="h-5 w-5 mr-2 text-yellow-500" />
                      Climate & Weather
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {city.climate.type && (
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Climate Type</p>
                          <p className="font-medium">{city.climate.type}</p>
                        </div>
                      )}
                      {city.climate.bestTimeToVisit && (
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Best Time to Visit</p>
                          <p className="font-medium">{city.climate.bestTimeToVisit}</p>
                        </div>
                      )}
                      {city.climate.averageTemperature?.summer && (
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Summer Temperature</p>
                          <p className="font-medium">{city.climate.averageTemperature.summer}°C</p>
                        </div>
                      )}
                      {city.climate.averageTemperature?.winter && (
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Winter Temperature</p>
                          <p className="font-medium">{city.climate.averageTemperature.winter}°C</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Transportation */}
                {city.transportation && (
                  <div className="bg-white rounded-xl p-6 shadow-sm">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Getting There</h3>
                    <div className="space-y-4">
                      {city.transportation.howToReach?.byAir && (
                        <div className="flex items-start space-x-3">
                          <PaperAirplaneIcon className="h-5 w-5 text-blue-500 mt-1" />
                          <div>
                            <h4 className="font-medium text-gray-900">By Air</h4>
                            <p className="text-sm text-gray-600">{city.transportation.howToReach.byAir}</p>
                          </div>
                        </div>
                      )}
                      {city.transportation.howToReach?.byRail && (
                        <div className="flex items-start space-x-3">
                          <BuildingOfficeIcon className="h-5 w-5 text-green-500 mt-1" />
                          <div>
                            <h4 className="font-medium text-gray-900">By Rail</h4>
                            <p className="text-sm text-gray-600">{city.transportation.howToReach.byRail}</p>
                          </div>
                        </div>
                      )}
                      {city.transportation.howToReach?.byRoad && (
                        <div className="flex items-start space-x-3">
                          <TruckIcon className="h-5 w-5 text-gray-500 mt-1" />
                          <div>
                            <h4 className="font-medium text-gray-900">By Road</h4>
                            <p className="text-sm text-gray-600">{city.transportation.howToReach.byRoad}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="space-y-8">
                <Comments
                  entityType="city"
                  entityId={city.id}
                  entitySlug={city.slug}
                />
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Facts */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Facts</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <UsersIcon className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">Population</span>
                  </div>
                  <span className="text-sm font-medium">{city.population?.toLocaleString()}</span>
                </div>
                {city.area && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <GlobeAltIcon className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">Area</span>
                    </div>
                    <span className="text-sm font-medium">{city.area} km²</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <ClockIcon className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">Timezone</span>
                  </div>
                  <span className="text-sm font-medium">{city.timezone}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CurrencyDollarIcon className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">Currency</span>
                  </div>
                  <span className="text-sm font-medium">{city.currency}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <LanguageIcon className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">Languages</span>
                  </div>
                  <span className="text-sm font-medium">{city.languages?.join(', ') || 'Not specified'}</span>
                </div>
              </div>
            </div>

            {/* Airports */}
            {city.transportation?.airports && city.transportation.airports.length > 0 && (
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Airports</h3>
                <div className="space-y-3">
                  {city.transportation.airports.map((airport, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <div>
                        <p className="font-medium text-gray-900">{airport.name}</p>
                        <p className="text-sm text-gray-600">{airport.code}</p>
                      </div>
                      <span className="text-sm text-gray-600">{airport.distance} km</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Local Transport */}
            {city.transportation?.localTransport && city.transportation.localTransport.length > 0 && (
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Local Transport</h3>
                <div className="flex flex-wrap gap-2">
                  {city.transportation.localTransport.map((transport, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                    >
                      {transport}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Place Card Component
function PlaceCard({ place }: { place: Place }) {
  const primaryImage = place.images?.find(img => img.isPrimary) || place.images?.[0];

  // Simple fallback images for each category
  const getFallbackImage = (category: string) => {
    const fallbacks = {
      museum: 'https://images.unsplash.com/photo-1566139884669-4b9356b4c040?w=800&q=80',
      religious: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&q=80',
      restaurant: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=800&q=80',
      attraction: 'https://images.unsplash.com/photo-1511739001486-6bfe10ce785f?w=800&q=80',
      default: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800&q=80'
    };

    return fallbacks[category as keyof typeof fallbacks] || fallbacks.default;
  };

  // Get the image URL with fallback
  const getImageUrl = () => {
    if (primaryImage?.url) {
      return primaryImage.url;
    }
    return getFallbackImage(place.category);
  };

  const imageUrl = getImageUrl();

  return (
    <Link href={`/places/${place.slug}`} className="group">
      <div className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden h-full">
        <div className="relative h-48 overflow-hidden bg-gray-200">
          <img
            src={imageUrl}
            alt={primaryImage?.alt || place.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            onError={(e) => {
              // If image fails to load, use category fallback
              const target = e.target as HTMLImageElement;
              target.src = getFallbackImage(place.category);
            }}
          />
          <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
            <DisplayStarRating rating={place.averageRating || 0} size="sm" showValue={false} />
            <span className="text-xs font-medium text-gray-700">{(place.averageRating || 0).toFixed(1)}</span>
          </div>
        </div>
        <div className="p-4">
          <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-1">
            {place.name}
          </h3>
          <p className="text-sm text-gray-600 line-clamp-2 mb-3">{place.description}</p>
          <div className="flex items-center justify-between">
            <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full capitalize">
              {place.category}
            </span>
            {place.pricing?.entryFee && (
              <span className="text-sm font-medium text-gray-900">
                €{place.pricing.entryFee.adult}
              </span>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}

// Helper function to group places by category
function groupPlacesByCategory(places: Place[]): Record<string, Place[]> {
  return places.reduce((acc, place) => {
    const category = place.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(place);
    return acc;
  }, {} as Record<string, Place[]>);
}

// Category icon component
function CategoryIcon({ category, className }: { category: string; className?: string }) {
  switch (category) {
    case 'attraction':
      return <SparklesIcon className={className} />;
    case 'museum':
      return <BuildingLibraryIcon className={className} />;
    case 'restaurant':
      return <HomeModernIcon className={className} />;
    case 'hotel':
      return <BuildingOfficeIcon className={className} />;
    case 'shopping':
      return <ShoppingBagIcon className={className} />;
    case 'entertainment':
      return <MusicalNoteIcon className={className} />;
    case 'nature':
      return <GlobeAltIcon className={className} />;
    case 'religious':
      return <BuildingLibraryIcon className={className} />;
    case 'historical':
      return <BuildingLibraryIcon className={className} />;
    default:
      return <MapPinIcon className={className} />;
  }
}
