const axios = require('axios');

async function testEndpoint() {
  try {
    console.log('🧪 Testing admin login...');
    
    // Test admin login
    const loginResponse = await axios.post('http://localhost:5001/api/admin/auth/login', {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Admin login successful');
      const token = loginResponse.data.data.token;
      
      // Test dashboard activity (the failing endpoint)
      console.log('🧪 Testing dashboard activity...');
      const activityResponse = await axios.get('http://localhost:5001/api/admin/dashboard/activity', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (activityResponse.data.success) {
        console.log('✅ Dashboard activity successful:', activityResponse.data.data.length, 'activities');
        console.log('📊 Sample activity:', activityResponse.data.data[0]);
      } else {
        console.error('❌ Dashboard activity failed:', activityResponse.data);
      }
      
    } else {
      console.error('❌ Admin login failed:', loginResponse.data);
    }
    
  } catch (error) {
    if (error.response) {
      console.error('❌ Test failed:', error.response.status, error.response.data);
    } else {
      console.error('❌ Test failed:', error.message);
    }
  }
}

testEndpoint();
