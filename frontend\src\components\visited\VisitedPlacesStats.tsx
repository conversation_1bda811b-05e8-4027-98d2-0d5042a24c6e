'use client';

import { 
  MapPinIcon, 
  StarIcon, 
  BuildingOfficeIcon,
  GlobeAltIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

interface VisitedPlacesStatsProps {
  stats: {
    totalVisited: number;
    averageRating: number;
    categoriesVisited: number;
    countriesVisited: number;
    citiesVisited: number;
  };
}

export function VisitedPlacesStats({ stats }: VisitedPlacesStatsProps) {
  const statItems = [
    {
      label: 'Places Visited',
      value: stats.totalVisited,
      icon: MapPinIcon,
      color: 'bg-blue-500',
      textColor: 'text-blue-600'
    },
    {
      label: 'Average Rating',
      value: stats.averageRating > 0 ? stats.averageRating.toFixed(1) : '0.0',
      icon: StarIcon,
      color: 'bg-yellow-500',
      textColor: 'text-yellow-600'
    },
    {
      label: 'Cities Explored',
      value: stats.citiesVisited,
      icon: BuildingOfficeIcon,
      color: 'bg-green-500',
      textColor: 'text-green-600'
    },
    {
      label: 'Countries Visited',
      value: stats.countriesVisited,
      icon: GlobeAltIcon,
      color: 'bg-purple-500',
      textColor: 'text-purple-600'
    },
    {
      label: 'Categories Explored',
      value: stats.categoriesVisited,
      icon: ChartBarIcon,
      color: 'bg-indigo-500',
      textColor: 'text-indigo-600'
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
      {statItems.map((item, index) => (
        <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className={`p-3 rounded-lg ${item.color}`}>
              <item.icon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-gray-900">{item.value}</p>
              <p className={`text-sm font-medium ${item.textColor}`}>{item.label}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
