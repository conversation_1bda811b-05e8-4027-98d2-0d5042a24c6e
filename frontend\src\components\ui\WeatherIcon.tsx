import { SunIcon, CloudIcon, CloudArrowDownIcon } from '@heroicons/react/24/outline';

interface WeatherIconProps {
  climateType: string;
  className?: string;
  showLabel?: boolean;
}

const climateIcons: Record<string, { icon: React.ComponentType<any>; emoji: string; color: string }> = {
  'Oceanic': { icon: CloudIcon, emoji: '🌊', color: 'text-blue-500' },
  'Mediterranean': { icon: SunIcon, emoji: '☀️', color: 'text-yellow-500' },
  'Humid subtropical': { icon: CloudArrowDownIcon, emoji: '🌧️', color: 'text-green-500' },
  'Humid continental': { icon: CloudIcon, emoji: '❄️', color: 'text-blue-600' },
  'Desert': { icon: SunIcon, emoji: '🏜️', color: 'text-orange-500' },
  'Tropical': { icon: SunIcon, emoji: '🌴', color: 'text-green-400' },
  'Temperate': { icon: CloudIcon, emoji: '🌤️', color: 'text-gray-500' },
  'Continental': { icon: CloudIcon, emoji: '🌨️', color: 'text-blue-700' },
  'Arid': { icon: SunIcon, emoji: '🌵', color: 'text-yellow-600' },
  'Subarctic': { icon: CloudIcon, emoji: '🧊', color: 'text-blue-800' }
};

export function WeatherIcon({ climateType, className = '', showLabel = false }: WeatherIconProps) {
  const climate = climateIcons[climateType] || climateIcons['Temperate'];
  const IconComponent = climate.icon;
  
  return (
    <span className={`inline-flex items-center space-x-1 ${className}`}>
      <span className="text-lg" role="img" aria-label={`${climateType} climate`}>
        {climate.emoji}
      </span>
      {showLabel && (
        <span className="text-sm text-gray-600">{climateType}</span>
      )}
    </span>
  );
}

// Helper function to get climate emoji
export function getClimateEmoji(climateType: string): string {
  return climateIcons[climateType]?.emoji || '🌤️';
}

// Helper function to get climate color
export function getClimateColor(climateType: string): string {
  return climateIcons[climateType]?.color || 'text-gray-500';
}
