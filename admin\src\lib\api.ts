import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  ApiResponse, 
  PaginatedResponse, 
  AdminUser, 
  User, 
  City, 
  Place, 
  Review,
  DashboardStats,
  UserFilters,
  CityFilters,
  PlaceFilters,
  ReviewFilters,
  LoginForm,
  CityForm,
  PlaceForm
} from '@/types';

class AdminApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('adminToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('adminToken');
          localStorage.removeItem('adminUser');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Authentication
  async login(credentials: LoginForm): Promise<ApiResponse<{ token: string; admin: AdminUser }>> {
    const response = await this.client.post('/admin/auth/login', credentials);
    return response.data;
  }

  async getProfile(): Promise<ApiResponse<{ admin: AdminUser }>> {
    const response = await this.client.get('/admin/auth/me');
    return response.data;
  }

  async updateProfile(data: Partial<AdminUser>): Promise<ApiResponse<{ admin: AdminUser }>> {
    const response = await this.client.put('/admin/auth/profile', data);
    return response.data;
  }

  async changePassword(data: { currentPassword: string; newPassword: string }): Promise<ApiResponse<any>> {
    const response = await this.client.put('/admin/auth/password', data);
    return response.data;
  }

  async logout(): Promise<ApiResponse<any>> {
    const response = await this.client.post('/admin/auth/logout');
    return response.data;
  }

  // Dashboard
  async getDashboardStats(): Promise<ApiResponse<DashboardStats>> {
    const response = await this.client.get('/admin/dashboard/stats');
    return response.data;
  }

  async getRecentActivity(): Promise<ApiResponse<any[]>> {
    const response = await this.client.get('/admin/dashboard/activity');
    return response.data;
  }

  // User Management
  async getUsers(filters?: UserFilters): Promise<PaginatedResponse<User[]>> {
    const response = await this.client.get('/admin/users', { params: filters });
    return response.data;
  }

  async getUser(id: string): Promise<ApiResponse<User>> {
    const response = await this.client.get(`/admin/users/${id}`);
    return response.data;
  }

  async updateUser(id: string, data: Partial<User>): Promise<ApiResponse<User>> {
    const response = await this.client.put(`/admin/users/${id}`, data);
    return response.data;
  }

  async updateUserStatus(userId: string, isActive: boolean): Promise<ApiResponse<any>> {
    try {
      const response = await this.client.patch(`/admin/users/${userId}/status`, { isActive });
      return { success: true, data: response.data };
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to update user status' };
    }
  }

  async deleteUser(id: string): Promise<ApiResponse<any>> {
    const response = await this.client.delete(`/admin/users/${id}`);
    return response.data;
  }

  async toggleUserStatus(id: string): Promise<ApiResponse<User>> {
    const response = await this.client.patch(`/admin/users/${id}/toggle-status`);
    return response.data;
  }

  async bulkDeleteUsers(ids: string[]): Promise<ApiResponse<any>> {
    const response = await this.client.post('/admin/users/bulk-delete', { ids });
    return response.data;
  }

  // City Management
  async getCities(filters?: CityFilters): Promise<PaginatedResponse<City[]>> {
    const response = await this.client.get('/admin/cities', { params: filters });
    return response.data;
  }



  async getCity(id: string): Promise<ApiResponse<City>> {
    const response = await this.client.get(`/admin/cities/${id}`);
    return response.data;
  }

  async createCity(data: CityForm): Promise<ApiResponse<City>> {
    const response = await this.client.post('/admin/cities', data);
    return response.data;
  }

  async updateCity(id: string, data: Partial<CityForm>): Promise<ApiResponse<City>> {
    const response = await this.client.put(`/admin/cities/${id}`, data);
    return response.data;
  }

  async deleteCity(id: string): Promise<ApiResponse<any>> {
    const response = await this.client.delete(`/admin/cities/${id}`);
    return response.data;
  }

  async toggleCityStatus(id: string): Promise<ApiResponse<City>> {
    const response = await this.client.patch(`/admin/cities/${id}/toggle-status`);
    return response.data;
  }

  async toggleCityFeatured(id: string): Promise<ApiResponse<City>> {
    const response = await this.client.patch(`/admin/cities/${id}/toggle-featured`);
    return response.data;
  }

  async bulkDeleteCities(ids: string[]): Promise<ApiResponse<any>> {
    const response = await this.client.post('/admin/cities/bulk-delete', { ids });
    return response.data;
  }

  // Place Management
  async getPlaces(filters?: PlaceFilters): Promise<PaginatedResponse<Place[]>> {
    const response = await this.client.get('/admin/places', { params: filters });
    return response.data;
  }



  async getPlace(id: string): Promise<ApiResponse<Place>> {
    const response = await this.client.get(`/admin/places/${id}`);
    return response.data;
  }

  async createPlace(data: PlaceForm): Promise<ApiResponse<Place>> {
    const response = await this.client.post('/admin/places', data);
    return response.data;
  }

  async updatePlace(id: string, data: Partial<PlaceForm>): Promise<ApiResponse<Place>> {
    const response = await this.client.put(`/admin/places/${id}`, data);
    return response.data;
  }

  async deletePlace(id: string): Promise<ApiResponse<any>> {
    const response = await this.client.delete(`/admin/places/${id}`);
    return response.data;
  }

  async togglePlaceStatus(id: string): Promise<ApiResponse<Place>> {
    const response = await this.client.patch(`/admin/places/${id}/toggle-status`);
    return response.data;
  }

  async togglePlaceFeatured(id: string): Promise<ApiResponse<Place>> {
    const response = await this.client.patch(`/admin/places/${id}/toggle-featured`);
    return response.data;
  }

  async bulkDeletePlaces(ids: string[]): Promise<ApiResponse<any>> {
    const response = await this.client.post('/admin/places/bulk-delete', { ids });
    return response.data;
  }

  // Review Management
  async getReviews(filters?: ReviewFilters): Promise<PaginatedResponse<Review[]>> {
    const response = await this.client.get('/admin/reviews', { params: filters });
    return response.data;
  }



  async getReview(id: string): Promise<ApiResponse<Review>> {
    const response = await this.client.get(`/admin/reviews/${id}`);
    return response.data;
  }

  async approveReview(id: string): Promise<ApiResponse<Review>> {
    const response = await this.client.patch(`/admin/reviews/${id}/approve`);
    return response.data;
  }

  async rejectReview(id: string): Promise<ApiResponse<Review>> {
    const response = await this.client.patch(`/admin/reviews/${id}/reject`);
    return response.data;
  }

  async deleteReview(id: string): Promise<ApiResponse<any>> {
    const response = await this.client.delete(`/admin/reviews/${id}`);
    return response.data;
  }

  async bulkApproveReviews(ids: string[]): Promise<ApiResponse<any>> {
    const response = await this.client.post('/admin/reviews/bulk-approve', { ids });
    return response.data;
  }

  async bulkRejectReviews(ids: string[]): Promise<ApiResponse<any>> {
    const response = await this.client.post('/admin/reviews/bulk-reject', { ids });
    return response.data;
  }

  // Media Management
  async uploadImage(file: File, folder?: string): Promise<ApiResponse<{ url: string; filename: string }>> {
    const formData = new FormData();
    formData.append('image', file);
    if (folder) formData.append('folder', folder);

    const response = await this.client.post('/admin/media/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async getMediaFiles(folder?: string, params?: {
    type?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<any>> {
    const queryParams = { folder, ...params };
    const response = await this.client.get('/admin/media', { params: queryParams });
    return response.data;
  }

  async uploadMediaFile(file: File, folder?: string): Promise<ApiResponse<any>> {
    const formData = new FormData();
    formData.append('file', file);
    if (folder) formData.append('folder', folder);

    const response = await this.client.post('/admin/media/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async deleteMediaFile(filename: string, folder?: string): Promise<ApiResponse<any>> {
    const params = folder ? { folder } : {};
    const response = await this.client.delete(`/admin/media/${filename}`, { params });
    return response.data;
  }

  async getMediaFolders(): Promise<ApiResponse<string[]>> {
    const response = await this.client.get('/admin/media/folders');
    return response.data;
  }

  async createMediaFolder(name: string): Promise<ApiResponse<any>> {
    const response = await this.client.post('/admin/media/folders', { name });
    return response.data;
  }

  async getMediaStats(): Promise<ApiResponse<any>> {
    const response = await this.client.get('/admin/media/stats');
    return response.data;
  }

  // Settings Management
  async getSettings(): Promise<ApiResponse<any>> {
    const response = await this.client.get('/admin/settings');
    return response.data;
  }

  async updateSettings(data: any): Promise<ApiResponse<any>> {
    const response = await this.client.put('/admin/settings', data);
    return response.data;
  }

  async getSystemInfo(): Promise<ApiResponse<any>> {
    const response = await this.client.get('/admin/settings/system');
    return response.data;
  }

  async testEmailConfig(email: string): Promise<ApiResponse<any>> {
    const response = await this.client.post('/admin/settings/test-email', { email });
    return response.data;
  }

  async clearCache(): Promise<ApiResponse<any>> {
    const response = await this.client.post('/admin/settings/clear-cache');
    return response.data;
  }

  async exportData(): Promise<ApiResponse<any>> {
    const response = await this.client.get('/admin/settings/export');
    return response.data;
  }

  async getBackupStatus(): Promise<ApiResponse<any>> {
    const response = await this.client.get('/admin/settings/backup-status');
    return response.data;
  }
}

export const adminApiClient = new AdminApiClient();
export default adminApiClient;
