apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongodb-deployment
  namespace: heritedge
  labels:
    app: mongodb
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      securityContext:
        fsGroup: 999
        runAsUser: 999
        runAsGroup: 999
      containers:
      - name: mongodb
        image: mongo:7.0
        ports:
        - containerPort: 27017
        args:
        - "--bind_ip_all"
        - "--auth"
        - "--wiredTigerCacheSizeGB=1"
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: password
        - name: MONGO_INITDB_DATABASE
          value: "heritedge"
        volumeMounts:
        - name: mongodb-storage
          mountPath: /data/db
        - name: mongodb-init-script
          mountPath: /docker-entrypoint-initdb.d
          readOnly: true
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          tcpSocket:
            port: 27017
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          tcpSocket:
            port: 27017
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: mongodb-storage
        persistentVolumeClaim:
          claimName: mongodb-pvc
      - name: mongodb-init-script
        configMap:
          name: mongodb-init-script
          defaultMode: 0755

---
apiVersion: v1
kind: Service
metadata:
  name: mongodb-service
  namespace: heritedge
  labels:
    app: mongodb
spec:
  selector:
    app: mongodb
  ports:
  - port: 27017
    targetPort: 27017
    protocol: TCP
  type: ClusterIP
