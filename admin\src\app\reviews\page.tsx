'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import AdminLayout from '@/components/layout/AdminLayout';
import { Review, ReviewFilters } from '@/types';
import { adminApiClient } from '@/lib/api';
import {
  MagnifyingGlassIcon,
  ChatBubbleLeftRightIcon,
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  EllipsisVerticalIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import toast from 'react-hot-toast';

export default function ReviewsPage() {
  const { admin } = useAuth();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedReviews, setSelectedReviews] = useState<string[]>([]);
  const [filters, setFilters] = useState<ReviewFilters>({
    page: 1,
    limit: 10,
    entityType: '',
    isApproved: undefined,
    isReported: undefined,
    rating: undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  useEffect(() => {
    fetchReviews();
  }, [filters]);

  const fetchReviews = async () => {
    try {
      setLoading(true);

      const response = await adminApiClient.getReviews(filters);

      if (response.success) {
        setReviews(response.data.items);
        setPagination(response.data.pagination);
      } else {
        throw new Error(response.error || 'Failed to fetch reviews');
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast.error('Failed to fetch reviews');

      // Fallback to mock data if API fails
      const mockReviews: Review[] = [
        {
          _id: '1',
          entityType: 'city',
          entityId: { _id: 'city1', name: 'Paris' },
          user: { _id: 'user1', name: 'John Doe', email: '<EMAIL>' },
          rating: 5,
          comment: 'Amazing city with incredible architecture and culture. Loved every moment of my visit!',
          isApproved: false,
          isReported: false,
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-15T10:30:00Z'
        },
        {
          _id: '2',
          entityType: 'place',
          entityId: { _id: 'place1', name: 'Eiffel Tower' },
          user: { _id: 'user2', name: 'Jane Smith', email: '<EMAIL>' },
          rating: 4,
          comment: 'Iconic landmark but very crowded. Best to visit early morning.',
          isApproved: true,
          isReported: false,
          createdAt: '2024-01-14T15:45:00Z',
          updatedAt: '2024-01-14T16:00:00Z'
        },
        {
          _id: '3',
          entityType: 'city',
          entityId: { _id: 'city2', name: 'Tokyo' },
          user: { _id: 'user3', name: 'Mike Johnson', email: '<EMAIL>' },
          rating: 2,
          comment: 'This place is terrible and overrated. Complete waste of money!',
          isApproved: false,
          isReported: true,
          createdAt: '2024-01-13T08:20:00Z',
          updatedAt: '2024-01-13T08:20:00Z'
        },
        {
          _id: '4',
          entityType: 'place',
          entityId: { _id: 'place2', name: 'Louvre Museum' },
          user: { _id: 'user4', name: 'Sarah Wilson', email: '<EMAIL>' },
          rating: 5,
          comment: 'Absolutely stunning collection. The Mona Lisa is smaller than expected but still amazing.',
          isApproved: true,
          isReported: false,
          createdAt: '2024-01-12T12:15:00Z',
          updatedAt: '2024-01-12T14:30:00Z'
        }
      ];

      setReviews(mockReviews);
      setPagination({
        page: 1,
        limit: 10,
        total: mockReviews.length,
        pages: 1
      });
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast.error('Failed to fetch reviews');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key: keyof ReviewFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1
    }));
  };

  const handleSelectReview = (reviewId: string) => {
    setSelectedReviews(prev => 
      prev.includes(reviewId) 
        ? prev.filter(id => id !== reviewId)
        : [...prev, reviewId]
    );
  };

  const handleSelectAll = () => {
    if (selectedReviews.length === reviews.length) {
      setSelectedReviews([]);
    } else {
      setSelectedReviews(reviews.map(review => review._id));
    }
  };

  const handleApproveReview = async (reviewId: string) => {
    try {
      const response = await adminApiClient.approveReview(reviewId);

      if (response.success) {
        setReviews(prev => prev.map(review =>
          review._id === reviewId
            ? { ...review, isApproved: true, isReported: false }
            : review
        ));
        toast.success('Review approved successfully');
      } else {
        throw new Error(response.error || 'Failed to approve review');
      }
    } catch (error) {
      console.error('Error approving review:', error);
      toast.error('Failed to approve review');
    }
  };

  const handleRejectReview = async (reviewId: string) => {
    try {
      const response = await adminApiClient.rejectReview(reviewId);

      if (response.success) {
        setReviews(prev => prev.map(review =>
          review._id === reviewId
            ? { ...review, isApproved: false }
            : review
        ));
        toast.success('Review rejected successfully');
      } else {
        throw new Error(response.error || 'Failed to reject review');
      }
    } catch (error) {
      console.error('Error rejecting review:', error);
      toast.error('Failed to reject review');
    }
  };

  const handleBulkApprove = async () => {
    try {
      const response = await adminApiClient.bulkApproveReviews(selectedReviews);

      if (response.success) {
        setReviews(prev => prev.map(review =>
          selectedReviews.includes(review._id)
            ? { ...review, isApproved: true, isReported: false }
            : review
        ));
        setSelectedReviews([]);
        toast.success(`${selectedReviews.length} reviews approved successfully`);
      } else {
        throw new Error(response.error || 'Failed to approve reviews');
      }
    } catch (error) {
      console.error('Error bulk approving reviews:', error);
      toast.error('Failed to approve reviews');
    }
  };

  const handleBulkReject = async () => {
    try {
      const response = await adminApiClient.bulkRejectReviews(selectedReviews);

      if (response.success) {
        setReviews(prev => prev.map(review =>
          selectedReviews.includes(review._id)
            ? { ...review, isApproved: false }
            : review
        ));
        setSelectedReviews([]);
        toast.success(`${selectedReviews.length} reviews rejected successfully`);
      } else {
        throw new Error(response.error || 'Failed to reject reviews');
      }
    } catch (error) {
      console.error('Error bulk rejecting reviews:', error);
      toast.error('Failed to reject reviews');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getStatusBadge = (review: Review) => {
    if (review.isReported) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
          Reported
        </span>
      );
    }
    if (review.isApproved) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckCircleIcon className="h-3 w-3 mr-1" />
          Approved
        </span>
      );
    }
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
        <ClockIcon className="h-3 w-3 mr-1" />
        Pending
      </span>
    );
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Reviews</h1>
            <p className="mt-1 text-sm text-gray-600">
              Moderate and manage user reviews
            </p>
          </div>
          {selectedReviews.length > 0 && (
            <div className="mt-4 sm:mt-0 flex space-x-2">
              <button
                onClick={handleBulkApprove}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <CheckCircleIcon className="h-4 w-4 mr-1" />
                Approve ({selectedReviews.length})
              </button>
              <button
                onClick={handleBulkReject}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <XCircleIcon className="h-4 w-4 mr-1" />
                Reject ({selectedReviews.length})
              </button>
            </div>
          )}
        </div>

        {/* Filters */}
        <div className="bg-white shadow rounded-lg">
          <div className="p-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
              {/* Entity Type Filter */}
              <select
                value={filters.entityType}
                onChange={(e) => handleFilterChange('entityType', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Types</option>
                <option value="city">Cities</option>
                <option value="place">Places</option>
              </select>

              {/* Approval Status Filter */}
              <select
                value={filters.isApproved === undefined ? '' : filters.isApproved.toString()}
                onChange={(e) => handleFilterChange('isApproved', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Status</option>
                <option value="true">Approved</option>
                <option value="false">Pending</option>
              </select>

              {/* Reported Filter */}
              <select
                value={filters.isReported === undefined ? '' : filters.isReported.toString()}
                onChange={(e) => handleFilterChange('isReported', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Reports</option>
                <option value="true">Reported</option>
                <option value="false">Not Reported</option>
              </select>

              {/* Rating Filter */}
              <select
                value={filters.rating || ''}
                onChange={(e) => handleFilterChange('rating', e.target.value ? parseInt(e.target.value) : undefined)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Ratings</option>
                <option value="5">5 Stars</option>
                <option value="4">4 Stars</option>
                <option value="3">3 Stars</option>
                <option value="2">2 Stars</option>
                <option value="1">1 Star</option>
              </select>

              {/* Sort */}
              <select
                value={`${filters.sortBy}-${filters.sortOrder}`}
                onChange={(e) => {
                  const [sortBy, sortOrder] = e.target.value.split('-');
                  handleFilterChange('sortBy', sortBy);
                  handleFilterChange('sortOrder', sortOrder);
                }}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="createdAt-desc">Newest First</option>
                <option value="createdAt-asc">Oldest First</option>
                <option value="rating-desc">Highest Rated</option>
                <option value="rating-asc">Lowest Rated</option>
              </select>
            </div>
          </div>
        </div>

        {/* Reviews List */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">Loading reviews...</p>
            </div>
          ) : reviews.length === 0 ? (
            <div className="p-8 text-center">
              <ChatBubbleLeftRightIcon className="h-12 w-12 text-gray-400 mx-auto" />
              <p className="mt-2 text-sm text-gray-500">No reviews found</p>
            </div>
          ) : (
            <>
              {/* Table Header */}
              <div className="px-6 py-3 border-b border-gray-200 bg-gray-50">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedReviews.length === reviews.length}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-3 text-sm font-medium text-gray-900">
                    Select All ({reviews.length})
                  </span>
                </div>
              </div>

              {/* Reviews */}
              <div className="divide-y divide-gray-200">
                {reviews.map((review) => (
                  <div key={review._id} className="p-6">
                    <div className="flex items-start space-x-4">
                      <input
                        type="checkbox"
                        checked={selectedReviews.includes(review._id)}
                        onChange={() => handleSelectReview(review._id)}
                        className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex items-center">
                              {renderStars(review.rating)}
                            </div>
                            <span className="text-sm font-medium text-gray-900">
                              {review.user.name}
                            </span>
                            <span className="text-sm text-gray-500">
                              reviewed {review.entityType === 'city' ? 'city' : 'place'}
                            </span>
                            <span className="text-sm font-medium text-blue-600">
                              {review.entityId.name}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {getStatusBadge(review)}
                            <Menu as="div" className="relative">
                              <Menu.Button className="flex items-center text-gray-400 hover:text-gray-600">
                                <EllipsisVerticalIcon className="h-5 w-5" />
                              </Menu.Button>
                              <Transition
                                as={Fragment}
                                enter="transition ease-out duration-100"
                                enterFrom="transform opacity-0 scale-95"
                                enterTo="transform opacity-100 scale-100"
                                leave="transition ease-in duration-75"
                                leaveFrom="transform opacity-100 scale-100"
                                leaveTo="transform opacity-0 scale-95"
                              >
                                <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                  <div className="py-1">
                                    {!review.isApproved && (
                                      <Menu.Item>
                                        {({ active }) => (
                                          <button
                                            onClick={() => handleApproveReview(review._id)}
                                            className={`${
                                              active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                                            } block w-full px-4 py-2 text-left text-sm`}
                                          >
                                            Approve Review
                                          </button>
                                        )}
                                      </Menu.Item>
                                    )}
                                    {review.isApproved && (
                                      <Menu.Item>
                                        {({ active }) => (
                                          <button
                                            onClick={() => handleRejectReview(review._id)}
                                            className={`${
                                              active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                                            } block w-full px-4 py-2 text-left text-sm`}
                                          >
                                            Reject Review
                                          </button>
                                        )}
                                      </Menu.Item>
                                    )}
                                    <Menu.Item>
                                      {({ active }) => (
                                        <button
                                          className={`${
                                            active ? 'bg-gray-100 text-red-900' : 'text-red-700'
                                          } block w-full px-4 py-2 text-left text-sm`}
                                        >
                                          Delete Review
                                        </button>
                                      )}
                                    </Menu.Item>
                                  </div>
                                </Menu.Items>
                              </Transition>
                            </Menu>
                          </div>
                        </div>
                        
                        <p className="mt-2 text-sm text-gray-700">{review.comment}</p>
                        
                        <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
                          <span>{review.user.email}</span>
                          <span>{formatDate(review.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
