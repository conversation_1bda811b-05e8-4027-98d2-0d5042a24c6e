'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';
import {
  MagnifyingGlassIcon,
  UsersIcon,
  GlobeAltIcon,
  ViewColumnsIcon,
  Squares2X2Icon,
  BookmarkIcon
} from '@heroicons/react/24/outline';
import {
  StarIcon as StarIconSolid,
  BookmarkIcon as BookmarkIconSolid
} from '@heroicons/react/24/solid';
import { apiClient } from '@/lib/api';
import { City, CityFilters } from '@/types';
import { Breadcrumb } from '@/components/ui/Breadcrumb';
import { DisplayStarRating } from '@/components/ui/StarRating';
import { cn, getImageUrl } from '@/lib/utils';
import { CountryFlag } from '@/components/ui/CountryFlag';
import { WeatherIcon } from '@/components/ui/WeatherIcon';

export default function CitiesPage() {
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filters, setFilters] = useState<CityFilters>({
    page: 1,
    limit: 12,
    sort: 'name'
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    pages: 0
  });

  // Fetch cities
  const fetchCities = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getCities(filters);
      if (response.success && response.data) {
        setCities(response.data.cities || []);
        setPagination(response.data.pagination || pagination);
      } else {
        setError('Failed to fetch cities');
      }
    } catch (err) {
      setError('Error loading cities');
      console.error('Error fetching cities:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch featured cities for initial load
  const fetchFeaturedCities = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getFeaturedCities();
      if (response.success && response.data) {
        setCities(response.data.cities || []);
      } else {
        setError('No featured cities found');
      }
    } catch (err) {
      console.error('Error fetching featured cities:', err);
      setError('Failed to load cities. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (Object.keys(filters).length > 3) {
      fetchCities();
    } else {
      fetchFeaturedCities();
    }
  }, [filters]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters(prev => ({
      ...prev,
      search: searchQuery,
      page: 1
    }));
  };

  // Handle filter change
  const handleFilterChange = (key: keyof CityFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1
    }));
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🏙️</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">No Cities Found</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => {
              setError(null);
              fetchFeaturedCities();
            }}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-white rounded-xl shadow-sm overflow-hidden">
                  <div className="h-48 bg-gray-200"></div>
                  <div className="p-6">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                    <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Explore Amazing Cities
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Discover the world's most beautiful destinations, from bustling metropolises to charming historic towns.
            </p>
            
            {/* Search Bar */}
            <form onSubmit={handleSearch} className="max-w-2xl mx-auto">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search cities..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 text-lg text-gray-900 bg-white rounded-xl shadow-lg focus:ring-4 focus:ring-blue-300 focus:outline-none"
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Search
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <Breadcrumb items={[{ label: 'Cities', isActive: true }]} />
        </div>
      </div>

      {/* Filters and View Controls */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-bold text-gray-900">
              {filters.search ? `Search results for "${filters.search}"` : 'All Cities'}
            </h2>
            <span className="text-gray-500">({cities.length} cities)</span>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-white rounded-lg shadow-sm border">
              <button
                onClick={() => setViewMode('grid')}
                className={cn(
                  'p-2 rounded-l-lg transition-colors',
                  viewMode === 'grid' ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-50'
                )}
              >
                <Squares2X2Icon className="h-5 w-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={cn(
                  'p-2 rounded-r-lg transition-colors',
                  viewMode === 'list' ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-50'
                )}
              >
                <ViewColumnsIcon className="h-5 w-5" />
              </button>
            </div>

            {/* Sort Dropdown */}
            <select
              value={filters.sort}
              onChange={(e) => handleFilterChange('sort', e.target.value)}
              className="px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="name">Name A-Z</option>
              <option value="-name">Name Z-A</option>
              <option value="-averageRating">Highest Rated</option>
              <option value="-totalReviews">Most Reviewed</option>
              <option value="-population">Population</option>
            </select>
          </div>
        </div>

        {/* Cities Grid/List */}
        {error ? (
          <div className="text-center py-12">
            <div className="text-red-600 mb-4">
              <GlobeAltIcon className="h-16 w-16 mx-auto mb-4" />
              <p className="text-lg font-medium">Error loading cities</p>
              <p className="text-sm">{error}</p>
            </div>
            <button
              onClick={() => fetchFeaturedCities()}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        ) : cities.length === 0 ? (
          <div className="text-center py-12">
            <GlobeAltIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-900 mb-2">No cities found</p>
            <p className="text-gray-600">Try adjusting your search or filters</p>
          </div>
        ) : (
          <div className={cn(
            viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
              : 'space-y-6'
          )}>
            {cities.map((city) => (
              <CityCard key={city.id} city={city} viewMode={viewMode} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// City Card Component
function CityCard({ city, viewMode }: { city: City; viewMode: 'grid' | 'list' }) {
  const { user, isAuthenticated } = useAuth();
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  const primaryImage = city.images.find(img => img.isPrimary) || city.images[0];
  const imageUrl = primaryImage ? getImageUrl(primaryImage.url) : '/assets/placeholders/city-placeholder.jpg';

  // Fetch bookmark status when user is authenticated
  useEffect(() => {
    const fetchStatus = async () => {
      if (!isAuthenticated || !city.id) return;

      try {
        const response = await apiClient.getCityStatus(city.id);
        if (response.success && response.data) {
          setIsBookmarked(response.data.isBookmarked);
        }
      } catch (error) {
        console.error('Error fetching city status:', error);
      }
    };

    fetchStatus();
  }, [isAuthenticated, city.id]);

  const handleBookmark = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      toast.error('Please login to bookmark cities');
      return;
    }

    if (!city.id) return;

    try {
      setActionLoading(true);
      const response = await apiClient.toggleCityBookmark(city.id);

      if (response.success) {
        setIsBookmarked(response.data.isBookmarked);
        toast.success(response.data.message);
      }
    } catch (error: any) {
      console.error('Bookmark error:', error);
      toast.error(error.message || 'Failed to update bookmark');
    } finally {
      setActionLoading(false);
    }
  };

  if (viewMode === 'list') {
    return (
      <Link href={`/cities/${city.slug}`} className="group">
        <div className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
          <div className="flex">
            <div className="w-1/3 h-48 relative overflow-hidden">
              <img
                src={imageUrl}
                alt={primaryImage?.alt || `${city.name} cityscape`}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div className="flex-1 p-6">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                  {city.name}
                </h3>
                <div className="flex items-center space-x-1">
                  <DisplayStarRating rating={city.averageRating || 0} size="sm" showValue={false} />
                  <span className="text-sm font-medium text-gray-700">{(city.averageRating || 0).toFixed(1)}</span>
                  <span className="text-sm text-gray-500">({city.totalReviews || 0})</span>
                </div>
              </div>
              <div className="flex items-center text-gray-600 mb-3">
                <CountryFlag country={city.country} className="mr-2" />
                <span className="text-sm">{city.country}</span>
                {city.population && (
                  <>
                    <UsersIcon className="h-4 w-4 ml-4 mr-1" />
                    <span className="text-sm">{(city.population / 1000000).toFixed(1)}M people</span>
                  </>
                )}
              </div>
              <div className="flex items-center text-gray-500 mb-3">
                <WeatherIcon climateType={city.climate.type} className="mr-2" />
                <span className="text-sm">{city.climate.type} • {city.climate.bestTimeToVisit}</span>
              </div>
              <p className="text-gray-600 text-sm line-clamp-2 mb-4">{city.description}</p>
              <div className="flex flex-wrap gap-2">
                {city.tags.slice(0, 3).map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  }

  return (
    <Link href={`/cities/${city.slug}`} className="group">
      <div className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
        <div className="relative h-48 overflow-hidden">
          <img
            src={imageUrl}
            alt={primaryImage?.alt || `${city.name} cityscape`}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <div className="absolute top-4 right-4 flex items-center space-x-2">
            <div className="bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
              <DisplayStarRating rating={city.averageRating || 0} size="sm" showValue={false} />
              <span className="text-xs font-medium text-gray-700">{(city.averageRating || 0).toFixed(1)}</span>
            </div>
            {isAuthenticated && (
              <button
                onClick={handleBookmark}
                disabled={actionLoading}
                className="p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors disabled:opacity-50"
                title={isBookmarked ? 'Remove bookmark' : 'Bookmark this city'}
              >
                {actionLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-700"></div>
                ) : isBookmarked ? (
                  <BookmarkIconSolid className="h-4 w-4 text-blue-600" />
                ) : (
                  <BookmarkIcon className="h-4 w-4 text-gray-700" />
                )}
              </button>
            )}
          </div>
        </div>
        <div className="p-6">
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
              {city.name}
            </h3>
          </div>
          <div className="flex items-center text-gray-600 mb-2">
            <CountryFlag country={city.country} className="mr-2" />
            <span className="text-sm">{city.country}</span>
            {city.population && (
              <>
                <UsersIcon className="h-4 w-4 ml-3 mr-1" />
                <span className="text-sm">{(city.population / 1000000).toFixed(1)}M</span>
              </>
            )}
          </div>
          <div className="flex items-center text-gray-500 mb-3">
            <WeatherIcon climateType={city.climate.type} className="mr-2" />
            <span className="text-xs">{city.climate.type}</span>
          </div>
          <p className="text-gray-600 text-sm line-clamp-2 mb-4">{city.description}</p>
          <div className="flex flex-wrap gap-2">
            {city.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      </div>
    </Link>
  );
}
