// Admin User Types
export interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: 'super_admin' | 'admin' | 'moderator';
  avatar?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Regular User Types (for management)
export interface User {
  _id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  isActive: boolean;
  profile?: {
    bio?: string;
    location?: string;
    website?: string;
    socialLinks?: {
      twitter?: string;
      instagram?: string;
      facebook?: string;
    };
  };
  preferences?: {
    newsletter: boolean;
    notifications: boolean;
    privacy: 'public' | 'private';
  };
  bookmarkedCities: string[];
  bookmarkedPlaces: string[];
  contributedPlaces: string[];
  visitedPlaces: any[];
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// City Types
export interface City {
  _id: string;
  name: string;
  slug: string;
  country: string;
  description: string;
  overview?: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  timezone?: string;
  currency?: string;
  languages?: string[];
  images: Array<{
    url: string;
    alt: string;
    isPrimary: boolean;
  }>;
  averageRating: number;
  totalReviews: number;
  isPublished: boolean;
  isFeatured: boolean;
  seoMetadata?: {
    title: string;
    description: string;
    keywords: string[];
  };
  createdAt: string;
  updatedAt: string;
}

// Place Types
export interface Place {
  _id: string;
  name: string;
  slug: string;
  description: string;
  category: string;
  city: City | string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  address?: string;
  website?: string;
  phone?: string;
  email?: string;
  openingHours?: {
    [key: string]: string;
  };
  priceRange?: string;
  images: Array<{
    url: string;
    alt: string;
    isPrimary: boolean;
  }>;
  amenities?: string[];
  tags?: string[];
  averageRating: number;
  totalReviews: number;
  isPublished: boolean;
  isFeatured: boolean;
  popularityScore: number;
  seoMetadata?: {
    title: string;
    description: string;
    keywords: string[];
  };
  createdAt: string;
  updatedAt: string;
}

// Review Types
export interface Review {
  _id: string;
  user: {
    _id: string;
    name: string;
    email: string;
  };
  entityType: 'city' | 'place';
  entityId: {
    _id: string;
    name: string;
  };
  rating: number;
  comment: string;
  isApproved: boolean;
  isReported: boolean;
  reportReason?: string;
  createdAt: string;
  updatedAt: string;
}

// Dashboard Stats Types
export interface DashboardStats {
  totalUsers: number;
  totalCities: number;
  totalPlaces: number;
  totalReviews: number;
  pendingReviews: number;
  recentUsers: number;
  recentCities: number;
  recentPlaces: number;
  averageRating: number;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
  data: {
    items: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

// Filter Types
export interface UserFilters {
  search?: string;
  role?: string;
  isActive?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CityFilters {
  search?: string;
  country?: string;
  isPublished?: boolean;
  isFeatured?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PlaceFilters {
  search?: string;
  category?: string;
  city?: string;
  isPublished?: boolean;
  isFeatured?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ReviewFilters {
  entityType?: 'city' | 'place';
  isApproved?: boolean;
  isReported?: boolean;
  rating?: number;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface CityForm {
  name: string;
  country: string;
  description: string;
  overview?: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  timezone?: string;
  currency?: string;
  languages?: string[];
  isPublished: boolean;
  isFeatured: boolean;
  seoMetadata?: {
    title: string;
    description: string;
    keywords: string[];
  };
}

export interface PlaceForm {
  name: string;
  description: string;
  category: string;
  city: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  address?: string;
  website?: string;
  phone?: string;
  email?: string;
  openingHours?: {
    [key: string]: string;
  };
  priceRange?: string;
  amenities?: string[];
  tags?: string[];
  isPublished: boolean;
  isFeatured: boolean;
  seoMetadata?: {
    title: string;
    description: string;
    keywords: string[];
  };
}
