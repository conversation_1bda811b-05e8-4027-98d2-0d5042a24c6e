interface CountryFlagProps {
  country: string;
  className?: string;
  showName?: boolean;
}

const countryFlags: Record<string, string> = {
  'France': '🇫🇷',
  'Japan': '🇯🇵',
  'United States': '🇺🇸',
  'United Kingdom': '🇬🇧',
  'Spain': '🇪🇸',
  'United Arab Emirates': '🇦🇪',
  'Italy': '🇮🇹',
  'Germany': '🇩🇪',
  'Australia': '🇦🇺',
  'Canada': '🇨🇦',
  'Brazil': '🇧🇷',
  'India': '🇮🇳',
  'China': '🇨🇳',
  'South Korea': '🇰🇷',
  'Thailand': '🇹🇭',
  'Greece': '🇬🇷',
  'Turkey': '🇹🇷',
  'Egypt': '🇪🇬',
  'South Africa': '🇿🇦',
  'Mexico': '🇲🇽',
  'Argentina': '🇦🇷',
  'Netherlands': '🇳🇱',
  'Switzerland': '🇨🇭',
  'Austria': '🇦🇹',
  'Portugal': '🇵🇹',
  'Norway': '🇳🇴',
  'Sweden': '🇸🇪',
  'Denmark': '🇩🇰',
  'Finland': '🇫🇮',
  'Russia': '🇷🇺',
  'Poland': '🇵🇱',
  'Czech Republic': '🇨🇿',
  'Hungary': '🇭🇺',
  'Croatia': '🇭🇷',
  'Morocco': '🇲🇦',
  'Singapore': '🇸🇬',
  'Malaysia': '🇲🇾',
  'Indonesia': '🇮🇩',
  'Philippines': '🇵🇭',
  'Vietnam': '🇻🇳',
  'New Zealand': '🇳🇿',
  'Chile': '🇨🇱',
  'Peru': '🇵🇪',
  'Colombia': '🇨🇴',
  'Israel': '🇮🇱',
  'Jordan': '🇯🇴',
  'Lebanon': '🇱🇧',
  'Qatar': '🇶🇦',
  'Kuwait': '🇰🇼',
  'Saudi Arabia': '🇸🇦',
  'Oman': '🇴🇲',
  'Bahrain': '🇧🇭'
};

export function CountryFlag({ country, className = '', showName = false }: CountryFlagProps) {
  const flag = countryFlags[country] || '🌍';
  
  return (
    <span className={`inline-flex items-center space-x-1 ${className}`}>
      <span className="text-lg" role="img" aria-label={`${country} flag`}>
        {flag}
      </span>
      {showName && (
        <span className="text-sm text-gray-600">{country}</span>
      )}
    </span>
  );
}

// Helper function to get just the flag emoji
export function getCountryFlag(country: string): string {
  return countryFlags[country] || '🌍';
}

// Helper function to get country code for API calls (if needed later)
export function getCountryCode(country: string): string {
  const countryCodes: Record<string, string> = {
    'France': 'FR',
    'Japan': 'JP',
    'United States': 'US',
    'United Kingdom': 'GB',
    'Spain': 'ES',
    'United Arab Emirates': 'AE',
    'Italy': 'IT',
    'Germany': 'DE',
    'Australia': 'AU',
    'Canada': 'CA',
    'Brazil': 'BR',
    'India': 'IN',
    'China': 'CN',
    'South Korea': 'KR',
    'Thailand': 'TH',
    'Greece': 'GR',
    'Turkey': 'TR',
    'Egypt': 'EG',
    'South Africa': 'ZA',
    'Mexico': 'MX',
    'Argentina': 'AR',
    'Netherlands': 'NL',
    'Switzerland': 'CH',
    'Austria': 'AT',
    'Portugal': 'PT',
    'Norway': 'NO',
    'Sweden': 'SE',
    'Denmark': 'DK',
    'Finland': 'FI',
    'Russia': 'RU',
    'Poland': 'PL',
    'Czech Republic': 'CZ',
    'Hungary': 'HU',
    'Croatia': 'HR',
    'Morocco': 'MA',
    'Singapore': 'SG',
    'Malaysia': 'MY',
    'Indonesia': 'ID',
    'Philippines': 'PH',
    'Vietnam': 'VN',
    'New Zealand': 'NZ',
    'Chile': 'CL',
    'Peru': 'PE',
    'Colombia': 'CO',
    'Israel': 'IL',
    'Jordan': 'JO',
    'Lebanon': 'LB',
    'Qatar': 'QA',
    'Kuwait': 'KW',
    'Saudi Arabia': 'SA',
    'Oman': 'OM',
    'Bahrain': 'BH'
  };
  
  return countryCodes[country] || 'XX';
}
