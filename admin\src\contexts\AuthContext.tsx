'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { AdminUser, LoginForm } from '@/types';
import { adminApiClient } from '@/lib/api';
import toast from 'react-hot-toast';

interface AuthContextType {
  admin: AdminUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginForm) => Promise<boolean>;
  logout: () => void;
  updateProfile: (data: Partial<AdminUser>) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [admin, setAdmin] = useState<AdminUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !!admin;

  // Check if user is logged in on mount
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('adminToken');
      const storedAdmin = localStorage.getItem('adminUser');

      if (token && storedAdmin) {
        try {
          // Verify token with API
          const response = await adminApiClient.getProfile();
          if (response.success && response.data) {
            setAdmin(response.data.admin);
          } else {
            // Token is invalid, clear storage
            localStorage.removeItem('adminToken');
            localStorage.removeItem('adminUser');
          }
        } catch {
          // Token is invalid, clear storage
          localStorage.removeItem('adminToken');
          localStorage.removeItem('adminUser');
        }
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const login = async (credentials: LoginForm): Promise<boolean> => {
    try {
      setIsLoading(true);

      const response = await adminApiClient.login(credentials);

      if (response.success && response.data) {
        const { token, admin: adminData } = response.data;

        // Store token and admin data
        localStorage.setItem('adminToken', token);
        localStorage.setItem('adminUser', JSON.stringify(adminData));

        setAdmin(adminData);
        toast.success('Login successful!');
        return true;
      } else {
        toast.error(response.error || 'Login failed');
        return false;
      }
    } catch (error: unknown) {
      console.error('Login error:', error);
      const errorMessage =
        typeof error === 'object' && error !== null && 'response' in error && (error as any).response?.data?.error
          ? (error as any).response.data.error
          : 'Login failed';
      toast.error(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await adminApiClient.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage and state
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminUser');
      setAdmin(null);
      toast.success('Logged out successfully');
      router.push('/login');
    }
  };

  const updateProfile = async (data: Partial<AdminUser>): Promise<boolean> => {
    try {
      const response = await adminApiClient.updateProfile(data);

      if (response.success && response.data) {
        const updatedAdmin = response.data.admin;
        setAdmin(updatedAdmin);
        localStorage.setItem('adminUser', JSON.stringify(updatedAdmin));
        toast.success('Profile updated successfully!');
        return true;
      } else {
        toast.error(response.error || 'Failed to update profile');
        return false;
      }
    } catch (error: unknown) {
      console.error('Update profile error:', error);
      const errorMessage =
        typeof error === 'object' && error !== null && 'response' in error && (error as any).response?.data?.error
          ? (error as any).response.data.error
          : 'Failed to update profile';
      toast.error(errorMessage);
      return false;
    }
  };

  const value: AuthContextType = {
    admin,
    isAuthenticated,
    isLoading,
    login,
    logout,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;
