const axios = require('axios');

async function testSettingsEndpoints() {
  try {
    console.log('🧪 Testing admin login...');
    
    // Test admin login
    const loginResponse = await axios.post('http://localhost:5001/api/admin/auth/login', {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Admin login successful');
      const token = loginResponse.data.data.token;
      const headers = { Authorization: `Bearer ${token}` };
      
      console.log('\n⚙️ Testing Settings Endpoints...');
      
      // Test get settings
      try {
        const settingsResponse = await axios.get('http://localhost:5001/api/admin/settings', { headers });
        console.log('✅ GET /api/admin/settings:', settingsResponse.status, settingsResponse.data.success ? 'SUCCESS' : 'FAILED');
        if (settingsResponse.data.success) {
          console.log('   🏢 Site name:', settingsResponse.data.data.siteName);
        }
      } catch (error) {
        console.log('❌ GET /api/admin/settings failed:', error.response?.status, error.response?.data?.error || error.message);
      }
      
      // Test get system info
      try {
        const systemResponse = await axios.get('http://localhost:5001/api/admin/settings/system', { headers });
        console.log('✅ GET /api/admin/settings/system:', systemResponse.status, systemResponse.data.success ? 'SUCCESS' : 'FAILED');
        if (systemResponse.data.success) {
          console.log('   💻 Node version:', systemResponse.data.data.nodeVersion);
        }
      } catch (error) {
        console.log('❌ GET /api/admin/settings/system failed:', error.response?.status, error.response?.data?.error || error.message);
      }
      
      // Test get backup status
      try {
        const backupResponse = await axios.get('http://localhost:5001/api/admin/settings/backup-status', { headers });
        console.log('✅ GET /api/admin/settings/backup-status:', backupResponse.status, backupResponse.data.success ? 'SUCCESS' : 'FAILED');
        if (backupResponse.data.success) {
          console.log('   💾 Status:', backupResponse.data.data.status);
        }
      } catch (error) {
        console.log('❌ GET /api/admin/settings/backup-status failed:', error.response?.status, error.response?.data?.error || error.message);
      }
      
      console.log('\n📁 Testing Media Endpoints...');
      
      // Test get media files
      try {
        const mediaResponse = await axios.get('http://localhost:5001/api/admin/media', { headers });
        console.log('✅ GET /api/admin/media:', mediaResponse.status, mediaResponse.data.success ? 'SUCCESS' : 'FAILED');
        if (mediaResponse.data.success) {
          console.log('   📊 Files found:', mediaResponse.data.data.items.length);
        }
      } catch (error) {
        console.log('❌ GET /api/admin/media failed:', error.response?.status, error.response?.data?.error || error.message);
      }
      
      // Test get media folders
      try {
        const foldersResponse = await axios.get('http://localhost:5001/api/admin/media/folders', { headers });
        console.log('✅ GET /api/admin/media/folders:', foldersResponse.status, foldersResponse.data.success ? 'SUCCESS' : 'FAILED');
        if (foldersResponse.data.success) {
          console.log('   📁 Folders found:', foldersResponse.data.data.length);
        }
      } catch (error) {
        console.log('❌ GET /api/admin/media/folders failed:', error.response?.status, error.response?.data?.error || error.message);
      }
      
      // Test get media stats
      try {
        const statsResponse = await axios.get('http://localhost:5001/api/admin/media/stats', { headers });
        console.log('✅ GET /api/admin/media/stats:', statsResponse.status, statsResponse.data.success ? 'SUCCESS' : 'FAILED');
        if (statsResponse.data.success) {
          console.log('   📊 Total files:', statsResponse.data.data.totalFiles);
        }
      } catch (error) {
        console.log('❌ GET /api/admin/media/stats failed:', error.response?.status, error.response?.data?.error || error.message);
      }
      
      console.log('\n🎉 All endpoint tests completed!');
      
    } else {
      console.error('❌ Admin login failed:', loginResponse.data);
    }
    
  } catch (error) {
    if (error.response) {
      console.error('❌ Test failed:', error.response.status, error.response.data);
    } else {
      console.error('❌ Test failed:', error.message);
    }
  }
}

testSettingsEndpoints();
