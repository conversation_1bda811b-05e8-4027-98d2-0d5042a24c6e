'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { VisitedPlace } from '@/types';
import { DisplayStarRating } from '@/components/ui/StarRating';
import { getImageUrl } from '@/lib/utils';
import { apiClient } from '@/lib/api';
import { EditVisitedPlaceModal } from './EditVisitedPlaceModal';
import {
  CalendarIcon,
  MapPinIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ChatBubbleLeftIcon
} from '@heroicons/react/24/outline';
import { StarIcon } from '@heroicons/react/24/solid';

interface VisitedPlaceCardProps {
  visitedPlace: VisitedPlace;
  viewMode: 'grid' | 'list';
  onUpdate: () => void;
  onRemove: () => void;
}

export function VisitedPlaceCard({ visitedPlace, viewMode, onUpdate, onRemove }: VisitedPlaceCardProps) {
  const [showEditModal, setShowEditModal] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const { place, visitDate, rating, review } = visitedPlace;

  const handleRemove = async () => {
    if (!confirm('Are you sure you want to remove this place from your visited list?')) {
      return;
    }

    try {
      setIsRemoving(true);
      const response = await apiClient.removeVisitedPlace(place.id);
      if (response.success) {
        onRemove();
      } else {
        alert('Failed to remove place');
      }
    } catch (error) {
      console.error('Error removing place:', error);
      alert('Failed to remove place');
    } finally {
      setIsRemoving(false);
    }
  };

  const handleEdit = () => {
    setShowEditModal(true);
  };

  const handleEditComplete = () => {
    setShowEditModal(false);
    onUpdate();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (viewMode === 'list') {
    return (
      <>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-start space-x-4">
            {/* Image */}
            <div className="flex-shrink-0">
              <Link href={`/places/${place.slug}`}>
                <div className="relative w-24 h-24 rounded-lg overflow-hidden">
                  <Image
                    src={getImageUrl(place.images?.[0]?.url)}
                    alt={place.name}
                    fill
                    className="object-cover hover:scale-105 transition-transform duration-200"
                  />
                </div>
              </Link>
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <Link href={`/places/${place.slug}`}>
                    <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                      {place.name}
                    </h3>
                  </Link>
                  
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                    <div className="flex items-center">
                      <MapPinIcon className="h-4 w-4 mr-1" />
                      <span>
                        {typeof place.city === 'object' && place.city !== null
                          ? `${place.city.name}, ${place.city.country}`
                          : place.city ?? ''}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <CalendarIcon className="h-4 w-4 mr-1" />
                      <span>Visited {formatDate(visitDate)}</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4 mt-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {place.category}
                    </span>
                    {place.averageRating > 0 && (
                      <div className="flex items-center space-x-1">
                        <DisplayStarRating rating={place.averageRating} size="sm" showValue={false} />
                        <span className="text-sm text-gray-600">{place.averageRating.toFixed(1)}</span>
                      </div>
                    )}
                  </div>

                  {/* Personal Rating and Review */}
                  {(rating || review) && (
                    <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                      {rating && (
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-sm font-medium text-gray-700">My Rating:</span>
                          <div className="flex items-center space-x-1">
                            {[...Array(5)].map((_, i) => (
                              <StarIcon
                                key={i}
                                className={`h-4 w-4 ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}
                              />
                            ))}
                            <span className="text-sm text-gray-600">({rating}/5)</span>
                          </div>
                        </div>
                      )}
                      {review && (
                        <div>
                          <span className="text-sm font-medium text-gray-700">My Review:</span>
                          <p className="text-sm text-gray-600 mt-1">{review}</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 ml-4">
                  <Link href={`/places/${place.slug}`}>
                    <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                      <EyeIcon className="h-5 w-5" />
                    </button>
                  </Link>
                  <button
                    onClick={handleEdit}
                    className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                  >
                    <PencilIcon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={handleRemove}
                    disabled={isRemoving}
                    className="p-2 text-gray-400 hover:text-red-600 transition-colors disabled:opacity-50"
                  >
                    <TrashIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {showEditModal && (
          <EditVisitedPlaceModal
            visitedPlace={visitedPlace}
            onClose={() => setShowEditModal(false)}
            onUpdate={handleEditComplete}
          />
        )}
      </>
    );
  }

  // Grid view
  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
        {/* Image */}
        <div className="relative h-48">
          <Link href={`/places/${place.slug}`}>
            <Image
              src={getImageUrl(place.images?.[0]?.url)}
              alt={place.name}
              fill
              className="object-cover hover:scale-105 transition-transform duration-200"
            />
          </Link>
          
          {/* Actions Overlay */}
          <div className="absolute top-3 right-3 flex space-x-1">
            <button
              onClick={handleEdit}
              className="p-2 bg-white/90 backdrop-blur-sm rounded-full text-gray-600 hover:text-green-600 transition-colors"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            <button
              onClick={handleRemove}
              disabled={isRemoving}
              className="p-2 bg-white/90 backdrop-blur-sm rounded-full text-gray-600 hover:text-red-600 transition-colors disabled:opacity-50"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>

          {/* Category Badge */}
          <div className="absolute bottom-3 left-3">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white/90 backdrop-blur-sm text-gray-800">
              {place.category}
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          <Link href={`/places/${place.slug}`}>
            <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors mb-2">
              {place.name}
            </h3>
          </Link>

          <div className="flex items-center text-sm text-gray-600 mb-3">
            <MapPinIcon className="h-4 w-4 mr-1" />
            <span>
              {typeof place.city === 'object' && place.city !== null
                ? `${place.city.name}, ${place.city.country}`
                : place.city ?? ''}
            </span>
          </div>

          <div className="flex items-center text-sm text-gray-600 mb-3">
            <CalendarIcon className="h-4 w-4 mr-1" />
            <span>Visited {formatDate(visitDate)}</span>
          </div>

          {place.averageRating > 0 && (
            <div className="flex items-center space-x-1 mb-3">
              <DisplayStarRating rating={place.averageRating} size="sm" showValue={false} />
              <span className="text-sm text-gray-600">{place.averageRating.toFixed(1)}</span>
              <span className="text-xs text-gray-500">({place.totalReviews} reviews)</span>
            </div>
          )}

          {/* Personal Rating and Review */}
          {(rating || review) && (
            <div className="mt-3 p-3 bg-blue-50 rounded-lg">
              {rating && (
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xs font-medium text-gray-700">My Rating:</span>
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <StarIcon
                        key={i}
                        className={`h-3 w-3 ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}
                      />
                    ))}
                    <span className="text-xs text-gray-600">({rating}/5)</span>
                  </div>
                </div>
              )}
              {review && (
                <div>
                  <div className="flex items-center space-x-1 mb-1">
                    <ChatBubbleLeftIcon className="h-3 w-3 text-gray-500" />
                    <span className="text-xs font-medium text-gray-700">My Review:</span>
                  </div>
                  <p className="text-xs text-gray-600 line-clamp-2">{review}</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {showEditModal && (
        <EditVisitedPlaceModal
          visitedPlace={visitedPlace}
          onClose={() => setShowEditModal(false)}
          onUpdate={handleEditComplete}
        />
      )}
    </>
  );
}
