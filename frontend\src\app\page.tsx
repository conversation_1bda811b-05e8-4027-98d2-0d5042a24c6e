'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRightIcon, MapPinIcon, StarIcon, UsersIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { apiClient } from '@/lib/api';
import { getImageUrl } from '@/lib/utils';
import { DisplayStarRating } from '@/components/ui/StarRating';

interface City {
  id: string;
  name: string;
  slug: string;
  country: string;
  description: string;
  images: Array<{
    url: string;
    alt: string;
    isPrimary: boolean;
  }>;
  averageRating: number;
  totalReviews: number;
}

export default function Home() {
  const [featuredCities, setFeaturedCities] = useState<City[]>([]);
  const [globalStats, setGlobalStats] = useState<any>({
    happyTravellers: 0,
    citiesCovered: 0,
    placesListed: 0,
    averageRating: 0.0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch featured cities and global stats in parallel
        const [citiesResponse, statsResponse] = await Promise.all([
          apiClient.getFeaturedCities(),
          apiClient.getGlobalStats()
        ]);

        if (citiesResponse.success && citiesResponse.data) {
          setFeaturedCities(citiesResponse.data.items || []);
        }

        if (statsResponse.success && statsResponse.data) {
          setGlobalStats(statsResponse.data);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-600 to-purple-700 text-white">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative mx-auto max-w-7xl px-4 py-24 sm:px-6 lg:px-8 lg:py-32">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl">
              Discover Amazing Cities
              <span className="block text-yellow-300">Around the World</span>
            </h1>
            <p className="mx-auto mt-6 max-w-2xl text-xl text-blue-100">
              Explore hidden gems, find the best attractions, and plan your perfect trip with HeritEdge.
              Your ultimate guide to world-class destinations.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/cities"
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-700 bg-white hover:bg-gray-50 transition-colors"
              >
                Explore Cities
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </Link>
              <Link
                href="/places"
                className="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-700 transition-colors"
              >
                Discover Places
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-gray-50 py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            <div className="text-center">
              <div className="flex justify-center">
                <MapPinIcon className="h-12 w-12 text-blue-600" />
              </div>
              <p className="mt-4 text-3xl font-bold text-gray-900">
                {loading ? (
                  <span className="inline-block w-12 h-8 bg-gray-200 rounded animate-pulse"></span>
                ) : (
                  globalStats.citiesCovered
                )}
              </p>
              <p className="text-gray-600">Cities Covered</p>
            </div>
            <div className="text-center">
              <div className="flex justify-center">
                <StarIcon className="h-12 w-12 text-yellow-500" />
              </div>
              <p className="mt-4 text-3xl font-bold text-gray-900">
                {loading ? (
                  <span className="inline-block w-12 h-8 bg-gray-200 rounded animate-pulse"></span>
                ) : (
                  globalStats.placesListed
                )}
              </p>
              <p className="text-gray-600">Places Listed</p>
            </div>
            <div className="text-center">
              <div className="flex justify-center">
                <UsersIcon className="h-12 w-12 text-green-600" />
              </div>
              <p className="mt-4 text-3xl font-bold text-gray-900">
                {loading ? (
                  <span className="inline-block w-12 h-8 bg-gray-200 rounded animate-pulse"></span>
                ) : (
                  globalStats.happyTravellers
                )}
              </p>
              <p className="text-gray-600">Happy Travelers</p>
            </div>
            <div className="text-center">
              <div className="flex justify-center">
                <StarIcon className="h-12 w-12 text-purple-600" />
              </div>
              <p className="mt-4 text-3xl font-bold text-gray-900">
                {loading ? (
                  <span className="inline-block w-12 h-8 bg-gray-200 rounded animate-pulse"></span>
                ) : (
                  globalStats.averageRating.toFixed(1)
                )}
              </p>
              <p className="text-gray-600">Average Rating</p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Cities Section */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Featured Destinations
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Discover the most popular cities loved by travelers worldwide
            </p>
          </div>

          <div className="mt-12 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {loading ? (
              // Loading skeleton
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="group relative overflow-hidden rounded-lg bg-white shadow-lg">
                  <div className="w-full h-48 bg-gray-200 animate-pulse"></div>
                  <div className="p-6">
                    <div className="h-6 bg-gray-200 rounded animate-pulse mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-1/3"></div>
                  </div>
                </div>
              ))
            ) : featuredCities.length > 0 ? (
              featuredCities.slice(0, 3).map((city) => {
                const primaryImage = city.images?.find(img => img.isPrimary) || city.images?.[0];
                const imageUrl = primaryImage ? getImageUrl(primaryImage.url) : '/assets/placeholders/city-placeholder.jpg';

                return (
                  <Link key={city.id} href={`/cities/${city.slug}`} className="group">
                    <div className="relative overflow-hidden rounded-lg bg-white shadow-lg hover:shadow-xl transition-all duration-300">
                      <div className="relative h-48 overflow-hidden">
                        <img
                          src={imageUrl}
                          alt={primaryImage?.alt || `${city.name} cityscape`}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
                          <DisplayStarRating rating={city.averageRating || 0} size="sm" showValue={false} />
                          <span className="text-xs font-medium text-gray-700">{(city.averageRating || 0).toFixed(1)}</span>
                        </div>
                      </div>
                      <div className="p-6">
                        <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                          {city.name}
                        </h3>
                        <p className="text-gray-600 mb-2">{city.country}</p>
                        <p className="text-gray-600 text-sm line-clamp-2 mb-3">{city.description}</p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-sm text-gray-500">
                            <DisplayStarRating rating={city.averageRating || 0} size="sm" showValue={false} />
                            <span className="ml-1">{(city.averageRating || 0).toFixed(1)}</span>
                            <span className="mx-1">•</span>
                            <span>{city.totalReviews || 0} reviews</span>
                          </div>
                          <div className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium text-sm">
                            Explore
                            <ArrowRightIcon className="ml-1 h-4 w-4" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                );
              })
            ) : (
              // No cities found
              <div className="col-span-full text-center py-12">
                <MapPinIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900 mb-2">No featured cities found</p>
                <p className="text-gray-600">Featured cities will appear here once they&apos;re available.</p>
              </div>
            )}
          </div>

          <div className="mt-12 text-center">
            <Link
              href="/cities"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              View All Cities
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600">
        <div className="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white sm:text-4xl">
              Ready to Start Your Journey?
            </h2>
            <p className="mt-4 text-xl text-blue-100">
              Join thousands of travelers discovering amazing places every day.
            </p>
            <div className="mt-8">
              <Link
                href="/auth/register"
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors"
              >
                Get Started Today
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
