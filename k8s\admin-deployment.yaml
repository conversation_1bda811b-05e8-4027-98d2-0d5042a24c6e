apiVersion: apps/v1
kind: Deployment
metadata:
  name: heritedge-admin-deployment
  namespace: heritedge
  labels:
    app: heritedge-admin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: heritedge-admin
  template:
    metadata:
      labels:
        app: heritedge-admin
    spec:
      containers:
      - name: heritedge-admin
        image: heritedge/admin:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: heritedge-config
              key: NODE_ENV
        - name: NEXT_PUBLIC_API_URL
          valueFrom:
            configMapKeyRef:
              name: heritedge-config
              key: NEXT_PUBLIC_API_URL
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3

---
apiVersion: v1
kind: Service
metadata:
  name: heritedge-admin-service
  namespace: heritedge
  labels:
    app: heritedge-admin
spec:
  selector:
    app: heritedge-admin
  ports:
  - port: 3001
    targetPort: 3001
    protocol: TCP
    name: http
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  name: heritedge-admin-nodeport
  namespace: heritedge
  labels:
    app: heritedge-admin
spec:
  selector:
    app: heritedge-admin
  ports:
  - port: 3001
    targetPort: 3001
    nodePort: 30002
    protocol: TCP
    name: http
  type: NodePort


