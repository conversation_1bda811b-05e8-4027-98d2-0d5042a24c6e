// Quick import script for MongoDB
// Run this script to import cities directly into MongoDB
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// MongoDB connection string - update as needed
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/heritedge';

// City schema (simplified for import)
const citySchema = new mongoose.Schema({
  name: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  country: { type: String, required: true },
  state: String,
  description: { type: String, required: true },
  overview: { type: String, required: true },
  images: [{
    url: String,
    alt: String,
    caption: String,
    isPrimary: { type: Boolean, default: false }
  }],
  coordinates: {
    latitude: { type: Number, required: true },
    longitude: { type: Number, required: true }
  },
  population: Number,
  area: Number,
  timezone: String,
  currency: String,
  languages: [String],
  climate: {
    type: String,
    bestTimeToVisit: String,
    averageTemperature: {
      summer: Number,
      winter: Number
    }
  },
  transportation: {
    howToReach: {
      byAir: String,
      byRoad: String,
      byRail: String
    },
    localTransport: [String],
    airports: [{
      name: String,
      code: String,
      distance: Number
    }]
  },
  economy: {
    majorIndustries: [String],
    gdp: Number
  },
  culture: {
    festivals: [String],
    traditions: [String],
    artAndCrafts: [String]
  },
  places: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Place' }],
  averageRating: { type: Number, default: 0 },
  totalReviews: { type: Number, default: 0 },
  isPublished: { type: Boolean, default: true },
  isFeatured: { type: Boolean, default: false },
  tags: [String],
  seoMetadata: {
    title: String,
    description: String,
    keywords: [String]
  },
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  lastUpdatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
}, {
  timestamps: true
});

const City = mongoose.model('City', citySchema);

async function importCities() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully');

    // Read cities data
    console.log('📖 Reading cities.json...');
    const filePath = path.join(__dirname, 'cities.json');
    const citiesData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    console.log(`📊 Found ${citiesData.length} cities to import`);

    // Clear existing cities (optional - comment out if you want to keep existing data)
    console.log('🗑️ Clearing existing cities...');
    await City.deleteMany({});
    console.log('✅ Cleared existing cities');

    // Import cities
    console.log('📍 Importing cities...');
    const importedCities = await City.insertMany(citiesData);
    console.log(`✅ Successfully imported ${importedCities.length} cities`);

    // Display imported cities
    console.log('\n🌍 Imported Cities:');
    importedCities.forEach((city, index) => {
      const flag = getCountryFlag(city.country);
      const featured = city.isFeatured ? '⭐' : '';
      console.log(`${index + 1}. ${flag} ${city.name}, ${city.country} ${featured}`);
    });

    // Verify import
    const totalCities = await City.countDocuments();
    const featuredCities = await City.countDocuments({ isFeatured: true });
    const publishedCities = await City.countDocuments({ isPublished: true });

    console.log('\n📊 Import Summary:');
    console.log(`- Total cities: ${totalCities}`);
    console.log(`- Featured cities: ${featuredCities}`);
    console.log(`- Published cities: ${publishedCities}`);

    console.log('\n🎉 Import completed successfully!');
    console.log('🌐 You can now view cities at: http://localhost:3001/cities');

  } catch (error) {
    console.error('❌ Import failed:', error.message);
    if (error.code === 11000) {
      console.error('💡 Duplicate key error - cities with these slugs already exist');
      console.error('💡 Try clearing the collection first or use different slugs');
    }
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

function getCountryFlag(country) {
  const flags = {
    'France': '🇫🇷',
    'Japan': '🇯🇵',
    'United Kingdom': '🇬🇧',
    'United States': '🇺🇸',
    'Spain': '🇪🇸',
    'Italy': '🇮🇹',
    'United Arab Emirates': '🇦🇪'
  };
  return flags[country] || '🌍';
}

// Run the import
if (require.main === module) {
  importCities();
}

module.exports = { importCities };
