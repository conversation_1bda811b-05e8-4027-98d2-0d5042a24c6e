import mongoose, { Schema, Document } from 'mongoose';

export interface IReview extends Document {
  entityType: 'city' | 'place';
  entityId: mongoose.Types.ObjectId;
  user: mongoose.Types.ObjectId;
  rating: number;
  comment: string;
  isApproved: boolean;
  isReported: boolean;
  reportReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

const ReviewSchema = new Schema<IReview>({
  entityType: {
    type: String,
    required: true,
    enum: ['city', 'place']
  },
  entityId: {
    type: Schema.Types.ObjectId,
    required: true,
    refPath: 'entityType'
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  rating: {
    type: Number,
    required: true,
    min: 1,
    max: 5
  },
  comment: {
    type: String,
    required: true,
    trim: true,
    minlength: 10,
    maxlength: 1000
  },
  isApproved: {
    type: Boolean,
    default: false  // Reviews require admin approval
  },
  isReported: {
    type: Boolean,
    default: false
  },
  reportReason: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Indexes
ReviewSchema.index({ entityType: 1, entityId: 1 });
ReviewSchema.index({ user: 1 });
ReviewSchema.index({ entityType: 1, entityId: 1, user: 1 }, { unique: true }); // One review per user per entity
ReviewSchema.index({ isApproved: 1 });
ReviewSchema.index({ isReported: 1 });

const Review = mongoose.model<IReview>('Review', ReviewSchema);
export default Review;
