'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import {
  BookmarkIcon,
  HeartIcon,
  MapPinIcon,
  StarIcon,
  EyeIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';
import {
  BookmarkIcon as BookmarkIconSolid,
  HeartIcon as HeartIconSolid,
  StarIcon as StarIconSolid
} from '@heroicons/react/24/solid';
import { apiClient } from '@/lib/api';
import { Breadcrumb } from '@/components/ui/Breadcrumb';
import toast from 'react-hot-toast';

interface BookmarkItem {
  _id: string;
  name: string;
  slug: string;
  category?: string;
  images?: { url: string; alt: string }[];
  city?: { name: string; slug: string; country: string };
  country?: string;
  averageRating?: number;
  description?: string;
}

export default function BookmarksPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [activeTab, setActiveTab] = useState<'places' | 'cities' | 'likes'>('places');
  const [bookmarks, setBookmarks] = useState<BookmarkItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredBookmarks, setFilteredBookmarks] = useState<BookmarkItem[]>([]);

  // Get tab from URL params
  useEffect(() => {
    const type = searchParams.get('type');
    if (type === 'cities' || type === 'likes') {
      setActiveTab(type);
    }
  }, [searchParams]);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch bookmarks
  useEffect(() => {
    const fetchBookmarks = async () => {
      if (!isAuthenticated) return;

      try {
        setLoading(true);
        setError(null);

        const response = await apiClient.getUserBookmarks(activeTab);

        if (response.success && response.data) {
          setBookmarks(response.data.bookmarks || []);
        }
      } catch (err: any) {
        console.error('Bookmarks error:', err);
        setError(err.message || 'Failed to load bookmarks');
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated) {
      fetchBookmarks();
    }
  }, [isAuthenticated, activeTab]);

  // Filter bookmarks based on search
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredBookmarks(bookmarks);
    } else {
      const filtered = bookmarks.filter(item =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.city?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.country?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredBookmarks(filtered);
    }
  }, [bookmarks, searchQuery]);

  const handleRemoveBookmark = async (itemId: string) => {
    try {
      if (activeTab === 'places') {
        await apiClient.togglePlaceBookmark(itemId);
      } else if (activeTab === 'cities') {
        await apiClient.toggleCityBookmark(itemId);
      } else if (activeTab === 'likes') {
        await apiClient.togglePlaceLike(itemId);
      }

      // Remove from local state
      setBookmarks(prev => prev.filter(item => item._id !== itemId));
      toast.success(`Removed from ${activeTab}`);
    } catch (error: any) {
      console.error('Remove bookmark error:', error);
      toast.error('Failed to remove bookmark');
    }
  };

  const getFallbackImage = (category?: string) => {
    const fallbacks = {
      museum: 'https://images.unsplash.com/photo-1566139884669-4b9356b4c040?w=400&q=80',
      religious: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&q=80',
      restaurant: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&q=80',
      attraction: 'https://images.unsplash.com/photo-1511739001486-6bfe10ce785f?w=400&q=80',
      default: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400&q=80'
    };
    return fallbacks[category as keyof typeof fallbacks] || fallbacks.default;
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <BookmarkIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Bookmarks</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'places', label: 'Bookmarked Places', icon: BookmarkIconSolid, count: bookmarks.length },
    { id: 'cities', label: 'Bookmarked Cities', icon: MapPinIcon, count: 0 },
    { id: 'likes', label: 'Liked Places', icon: HeartIconSolid, count: bookmarks.length }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <BookmarkIconSolid className="h-16 w-16 text-white mx-auto mb-4" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">My Bookmarks</h1>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Manage your saved places, cities, and liked destinations all in one place
            </p>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <Breadcrumb items={[{ label: 'Bookmarks', isActive: true }]} />
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    isActive
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-5 w-5" />
                  <span>{tab.label}</span>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    isActive ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder={`Search ${activeTab}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <button className="flex items-center space-x-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <AdjustmentsHorizontalIcon className="h-5 w-5" />
            <span>Filters</span>
          </button>
        </div>

        {/* Bookmarks Grid */}
        {filteredBookmarks.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredBookmarks.map((item) => (
              <div key={item._id} className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden group">
                <div className="relative">
                  <img
                    src={item.images?.[0]?.url || getFallbackImage(item.category)}
                    alt={item.name}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = getFallbackImage(item.category);
                    }}
                  />
                  <button
                    onClick={() => handleRemoveBookmark(item._id)}
                    className="absolute top-3 right-3 p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors"
                    title="Remove bookmark"
                  >
                    <TrashIcon className="h-4 w-4 text-red-600" />
                  </button>
                  {item.category && (
                    <span className="absolute top-3 left-3 px-2 py-1 bg-blue-600 text-white text-xs font-medium rounded-full capitalize">
                      {item.category}
                    </span>
                  )}
                </div>
                
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                    <Link href={`/${activeTab === 'cities' ? 'cities' : 'places'}/${item.slug}`}>
                      {item.name}
                    </Link>
                  </h3>
                  
                  {item.city && (
                    <div className="flex items-center text-gray-600 mb-2">
                      <MapPinIcon className="h-4 w-4 mr-1" />
                      <span className="text-sm">{item.city.name}, {item.city.country}</span>
                    </div>
                  )}
                  
                  {item.averageRating && (
                    <div className="flex items-center mb-3">
                      <StarIconSolid className="h-4 w-4 text-yellow-400 mr-1" />
                      <span className="text-sm font-medium text-gray-700">{item.averageRating}</span>
                    </div>
                  )}
                  
                  {item.description && (
                    <p className="text-gray-600 text-sm line-clamp-2 mb-4">
                      {item.description}
                    </p>
                  )}
                  
                  <Link
                    href={`/${activeTab === 'cities' ? 'cities' : 'places'}/${item.slug}`}
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium text-sm"
                  >
                    View Details
                    <EyeIcon className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <BookmarkIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No {activeTab} bookmarked yet
            </h3>
            <p className="text-gray-600 mb-6">
              Start exploring and bookmark your favorite {activeTab} to see them here.
            </p>
            <Link
              href={activeTab === 'cities' ? '/cities' : '/places'}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
              Explore {activeTab}
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
