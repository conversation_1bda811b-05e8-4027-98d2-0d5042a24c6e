import { Response } from 'express';
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler';
import { AdminAuthRequest } from './adminAuthController';
import path from 'path';
import fs from 'fs';
import { promisify } from 'util';

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);

interface MediaFile {
  filename: string;
  originalName: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: Date;
  folder?: string;
}

// @desc    Get all media files
// @route   GET /api/admin/media
// @access  Private (Admin)
export const getMediaFiles = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const { folder, type, search, page = 1, limit = 20 } = req.query;
  
  try {
    const uploadsDir = path.join(process.cwd(), 'uploads');
    const targetDir = folder ? path.join(uploadsDir, folder as string) : uploadsDir;
    
    // Ensure directory exists
    if (!fs.existsSync(targetDir)) {
      return res.json({
        success: true,
        data: {
          items: [],
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total: 0,
            pages: 0
          }
        }
      });
    }

    const files = await readdir(targetDir);
    const mediaFiles: MediaFile[] = [];

    for (const file of files) {
      const filePath = path.join(targetDir, file);
      const stats = await stat(filePath);
      
      if (stats.isFile()) {
        const ext = path.extname(file).toLowerCase();
        const isImage = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'].includes(ext);
        const isVideo = ['.mp4', '.avi', '.mov', '.wmv', '.flv'].includes(ext);
        const isDocument = ['.pdf', '.doc', '.docx', '.txt'].includes(ext);
        
        let fileType = 'other';
        if (isImage) fileType = 'image';
        else if (isVideo) fileType = 'video';
        else if (isDocument) fileType = 'document';

        // Apply type filter
        if (type && fileType !== type) continue;
        
        // Apply search filter
        if (search && !file.toLowerCase().includes((search as string).toLowerCase())) continue;

        const mediaFile: MediaFile = {
          filename: file,
          originalName: file,
          size: stats.size,
          type: fileType,
          url: `/uploads/${folder ? folder + '/' : ''}${file}`,
          uploadedAt: stats.mtime,
          folder: folder as string
        };

        mediaFiles.push(mediaFile);
      }
    }

    // Sort by upload date (newest first)
    mediaFiles.sort((a, b) => new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime());

    // Pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedFiles = mediaFiles.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        items: paginatedFiles,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: mediaFiles.length,
          pages: Math.ceil(mediaFiles.length / limitNum)
        }
      }
    });

  } catch (error) {
    console.error('Error getting media files:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get media files'
    });
  }
});

// @desc    Upload media file
// @route   POST /api/admin/media/upload
// @access  Private (Admin)
export const uploadMediaFile = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: 'No file uploaded'
    });
  }

  const { folder } = req.body;
  const file = req.file;

  try {
    const uploadsDir = path.join(process.cwd(), 'uploads');
    const targetDir = folder ? path.join(uploadsDir, folder) : uploadsDir;
    
    // Ensure directory exists
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const ext = path.extname(file.originalname);
    const filename = `${timestamp}-${Math.random().toString(36).substring(2)}${ext}`;
    const filePath = path.join(targetDir, filename);

    // Move file to target directory
    fs.renameSync(file.path, filePath);

    const mediaFile: MediaFile = {
      filename,
      originalName: file.originalname,
      size: file.size,
      type: file.mimetype.startsWith('image/') ? 'image' : 
            file.mimetype.startsWith('video/') ? 'video' : 'document',
      url: `/uploads/${folder ? folder + '/' : ''}${filename}`,
      uploadedAt: new Date(),
      folder
    };

    res.status(201).json({
      success: true,
      data: mediaFile
    });

  } catch (error) {
    console.error('Error uploading file:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to upload file'
    });
  }
});

// @desc    Delete media file
// @route   DELETE /api/admin/media/:filename
// @access  Private (Admin)
export const deleteMediaFile = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const { filename } = req.params;
  const { folder } = req.query;

  try {
    const uploadsDir = path.join(process.cwd(), 'uploads');
    const targetDir = folder ? path.join(uploadsDir, folder as string) : uploadsDir;
    const filePath = path.join(targetDir, filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        error: 'File not found'
      });
    }

    await unlink(filePath);

    res.json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting file:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete file'
    });
  }
});

// @desc    Get media folders
// @route   GET /api/admin/media/folders
// @access  Private (Admin)
export const getMediaFolders = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  try {
    const uploadsDir = path.join(process.cwd(), 'uploads');
    
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    const items = await readdir(uploadsDir);
    const folders: string[] = [];

    for (const item of items) {
      const itemPath = path.join(uploadsDir, item);
      const stats = await stat(itemPath);
      
      if (stats.isDirectory()) {
        folders.push(item);
      }
    }

    res.json({
      success: true,
      data: folders
    });

  } catch (error) {
    console.error('Error getting folders:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get folders'
    });
  }
});

// @desc    Create media folder
// @route   POST /api/admin/media/folders
// @access  Private (Admin)
export const createMediaFolder = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const { name } = req.body;

  if (!name || typeof name !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Folder name is required'
    });
  }

  try {
    const uploadsDir = path.join(process.cwd(), 'uploads');
    const folderPath = path.join(uploadsDir, name);

    if (fs.existsSync(folderPath)) {
      return res.status(400).json({
        success: false,
        error: 'Folder already exists'
      });
    }

    fs.mkdirSync(folderPath, { recursive: true });

    res.status(201).json({
      success: true,
      message: 'Folder created successfully',
      data: { name }
    });

  } catch (error) {
    console.error('Error creating folder:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create folder'
    });
  }
});

// @desc    Get media statistics
// @route   GET /api/admin/media/stats
// @access  Private (Admin)
export const getMediaStats = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  try {
    const uploadsDir = path.join(process.cwd(), 'uploads');
    
    if (!fs.existsSync(uploadsDir)) {
      return res.json({
        success: true,
        data: {
          totalFiles: 0,
          totalSize: 0,
          imageCount: 0,
          videoCount: 0,
          documentCount: 0,
          folderCount: 0
        }
      });
    }

    let totalFiles = 0;
    let totalSize = 0;
    let imageCount = 0;
    let videoCount = 0;
    let documentCount = 0;
    let folderCount = 0;

    const scanDirectory = async (dir: string) => {
      const items = await readdir(dir);
      
      for (const item of items) {
        const itemPath = path.join(dir, item);
        const stats = await stat(itemPath);
        
        if (stats.isDirectory()) {
          folderCount++;
          await scanDirectory(itemPath);
        } else {
          totalFiles++;
          totalSize += stats.size;
          
          const ext = path.extname(item).toLowerCase();
          if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'].includes(ext)) {
            imageCount++;
          } else if (['.mp4', '.avi', '.mov', '.wmv', '.flv'].includes(ext)) {
            videoCount++;
          } else if (['.pdf', '.doc', '.docx', '.txt'].includes(ext)) {
            documentCount++;
          }
        }
      }
    };

    await scanDirectory(uploadsDir);

    res.json({
      success: true,
      data: {
        totalFiles,
        totalSize,
        imageCount,
        videoCount,
        documentCount,
        folderCount
      }
    });

  } catch (error) {
    console.error('Error getting media stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get media statistics'
    });
  }
});
