# Use official Node.js LTS base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files and install all dependencies (including dev for build step)
COPY package*.json ./
RUN npm install

# Copy rest of the code and build the app
COPY . .

# Compile TypeScript to JavaScript
RUN npm run build

# Install only production dependencies for final image
RUN npm prune --production

# Set the command to run the app
CMD ["npm", "run", "start"]

# Expose port (if your API listens on 3000)
EXPOSE 5000
