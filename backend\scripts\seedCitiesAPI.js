const axios = require('axios');

const API_BASE_URL = 'http://localhost:5001/api';

const sampleCities = [
  {
    name: 'Paris',
    slug: 'paris',
    country: 'France',
    state: 'Île-de-France',
    description: 'The City of Light, known for its art, fashion, gastronomy, and culture.',
    overview: 'Paris, the capital of France, is a major European city and a global center for art, fashion, gastronomy and culture. Its 19th-century cityscape is crisscrossed by wide boulevards and the River Seine. Beyond such landmarks as the Eiffel Tower and the 12th-century, Gothic Notre-Dame cathedral, the city is known for its cafe culture and designer boutiques along the Rue du Faubourg Saint-Honoré.',
    images: [
      {
        url: 'https://images.unsplash.com/photo-1502602898536-47ad22581b52?w=800&h=600&fit=crop',
        alt: 'Paris skyline with Eiffel Tower',
        caption: 'The iconic Eiffel Tower dominates the Paris skyline',
        isPrimary: true
      }
    ],
    coordinates: { latitude: 48.8566, longitude: 2.3522 },
    population: 2161000,
    area: 105.4,
    timezone: 'Europe/Paris',
    currency: 'EUR',
    languages: ['French'],
    climate: {
      type: 'Oceanic',
      bestTimeToVisit: 'April to June, September to October',
      averageTemperature: { summer: 25, winter: 7 }
    },
    transportation: {
      howToReach: {
        byAir: 'Charles de Gaulle Airport (CDG) and Orly Airport (ORY) serve the city with flights from around the world.',
        byRoad: 'Well connected by highways from all major European cities. The A1, A4, A6, and A10 motorways provide access.',
        byRail: 'Gare du Nord, Gare de Lyon, and other major stations connect Paris to European cities via high-speed rail.'
      },
      localTransport: ['Metro', 'Bus', 'Tram', 'Vélib (bike sharing)', 'Taxi', 'Uber'],
      airports: [
        { name: 'Charles de Gaulle', code: 'CDG', distance: 25 },
        { name: 'Orly', code: 'ORY', distance: 13 }
      ]
    },
    economy: {
      majorIndustries: ['Tourism', 'Fashion', 'Technology', 'Finance', 'Luxury goods'],
      gdp: 739000000000
    },
    culture: {
      festivals: ['Fête de la Musique', 'Nuit Blanche', 'Paris Fashion Week', 'Festival d\'Automne'],
      traditions: ['Café culture', 'Sunday markets', 'Evening strolls along the Seine'],
      artAndCrafts: ['Fashion design', 'Perfumery', 'Culinary arts', 'Fine arts']
    },
    averageRating: 4.8,
    totalReviews: 15420,
    isPublished: true,
    isFeatured: true,
    tags: ['romantic', 'art', 'fashion', 'cuisine', 'historic', 'museums'],
    seoMetadata: {
      title: 'Paris - The City of Light | CityTales',
      description: 'Discover Paris, the romantic capital of France known for its iconic landmarks, world-class museums, and exquisite cuisine.',
      keywords: ['Paris', 'France', 'Eiffel Tower', 'Louvre', 'travel', 'tourism']
    }
  },
  {
    name: 'Tokyo',
    slug: 'tokyo',
    country: 'Japan',
    description: 'A bustling metropolis blending ultra-modern and traditional culture.',
    overview: 'Tokyo, Japan\'s busy capital, mixes the ultramodern and the traditional, from neon-lit skyscrapers to historic temples. The opulent Meiji Shinto Shrine is known for its towering gate and surrounding woods. The Imperial Palace sits amid large public gardens. The city\'s many museums offer exhibits ranging from classical art to a reconstructed kabuki theater.',
    images: [
      {
        url: 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=800&h=600&fit=crop',
        alt: 'Tokyo skyline at night',
        caption: 'Tokyo\'s neon-lit skyline showcases its modern architecture',
        isPrimary: true
      }
    ],
    coordinates: { latitude: 35.6762, longitude: 139.6503 },
    population: 13960000,
    area: 2194,
    timezone: 'Asia/Tokyo',
    currency: 'JPY',
    languages: ['Japanese'],
    climate: {
      type: 'Humid subtropical',
      bestTimeToVisit: 'March to May, September to November',
      averageTemperature: { summer: 30, winter: 10 }
    },
    transportation: {
      howToReach: {
        byAir: 'Narita International Airport (NRT) and Haneda Airport (HND) serve Tokyo with international and domestic flights.',
        byRoad: 'Connected by extensive highway network throughout Japan.',
        byRail: 'Tokyo Station is a major hub for the Shinkansen (bullet train) network.'
      },
      localTransport: ['JR Lines', 'Tokyo Metro', 'Private railways', 'Buses', 'Taxis'],
      airports: [
        { name: 'Narita International', code: 'NRT', distance: 60 },
        { name: 'Haneda', code: 'HND', distance: 20 }
      ]
    },
    economy: {
      majorIndustries: ['Technology', 'Finance', 'Manufacturing', 'Tourism', 'Entertainment'],
      gdp: 1617000000000
    },
    culture: {
      festivals: ['Cherry Blossom Festival', 'Kanda Festival', 'Sanja Festival', 'Tokyo Film Festival'],
      traditions: ['Tea ceremony', 'Kabuki theater', 'Sumo wrestling', 'Hanami (flower viewing)'],
      artAndCrafts: ['Origami', 'Calligraphy', 'Pottery', 'Woodworking']
    },
    averageRating: 4.7,
    totalReviews: 23150,
    isPublished: true,
    isFeatured: true,
    tags: ['modern', 'technology', 'culture', 'food', 'anime', 'temples'],
    seoMetadata: {
      title: 'Tokyo - Modern Metropolis | CityTales',
      description: 'Experience Tokyo, where cutting-edge technology meets ancient traditions in Japan\'s vibrant capital city.',
      keywords: ['Tokyo', 'Japan', 'modern', 'technology', 'temples', 'sushi']
    }
  },
  {
    name: 'London',
    slug: 'london',
    country: 'United Kingdom',
    state: 'England',
    description: 'A historic capital blending royal heritage with modern innovation.',
    overview: 'London, the capital of England and the United Kingdom, is a 21st-century city with history stretching back to Roman times. At its centre stand the imposing Houses of Parliament, the iconic \'Big Ben\' clock tower and Westminster Abbey, site of British monarch coronations. Across the Thames River, the London Eye observation wheel provides panoramic views of the South Bank cultural complex.',
    images: [
      {
        url: 'https://images.unsplash.com/photo-*************-59663e0ac1ad?w=800&h=600&fit=crop',
        alt: 'London skyline with Big Ben and Thames',
        caption: 'The iconic Big Ben and Houses of Parliament along the Thames River',
        isPrimary: true
      }
    ],
    coordinates: { latitude: 51.5074, longitude: -0.1278 },
    population: 9540000,
    area: 1572,
    timezone: 'Europe/London',
    currency: 'GBP',
    languages: ['English'],
    climate: {
      type: 'Oceanic',
      bestTimeToVisit: 'May to September',
      averageTemperature: { summer: 23, winter: 7 }
    },
    transportation: {
      howToReach: {
        byAir: 'Heathrow (LHR), Gatwick (LGW), Stansted (STN), and Luton (LTN) airports serve London with global connections.',
        byRoad: 'Connected by M25 orbital motorway and major routes from across the UK and Europe via Channel Tunnel.',
        byRail: 'Multiple stations including King\'s Cross St. Pancras (Eurostar), Paddington, Victoria, and Waterloo.'
      },
      localTransport: ['Underground (Tube)', 'Buses', 'Overground', 'DLR', 'Trams', 'Boris Bikes', 'Black Cabs'],
      airports: [
        { name: 'Heathrow', code: 'LHR', distance: 24 },
        { name: 'Gatwick', code: 'LGW', distance: 48 },
        { name: 'Stansted', code: 'STN', distance: 56 },
        { name: 'Luton', code: 'LTN', distance: 56 }
      ]
    },
    economy: {
      majorIndustries: ['Finance', 'Technology', 'Creative Industries', 'Tourism', 'Education', 'Healthcare'],
      gdp: 653000000000
    },
    culture: {
      festivals: ['Notting Hill Carnival', 'London Film Festival', 'Pride in London', 'Lord Mayor\'s Show'],
      traditions: ['Afternoon tea', 'Pub culture', 'Royal ceremonies', 'Theatre in West End'],
      artAndCrafts: ['Theatre', 'Literature', 'Fashion design', 'Contemporary art']
    },
    averageRating: 4.7,
    totalReviews: 38920,
    isPublished: true,
    isFeatured: true,
    tags: ['historic', 'royal', 'museums', 'theatre', 'multicultural', 'finance'],
    seoMetadata: {
      title: 'London - Historic Capital | CityTales',
      description: 'Discover London, where royal heritage meets modern innovation in the UK\'s vibrant capital city.',
      keywords: ['London', 'UK', 'Big Ben', 'British Museum', 'Thames', 'royal']
    }
  }
];

async function seedCitiesViaAPI() {
  try {
    console.log('🌍 Starting to seed cities via API...');
    
    // First, let's check if the API is running
    try {
      const healthCheck = await axios.get(`${API_BASE_URL}/health`);
      console.log('✅ API is running:', healthCheck.data);
    } catch (error) {
      console.error('❌ API is not running. Please start the backend server first.');
      return;
    }

    // Clear existing cities (if endpoint exists)
    try {
      console.log('🗑️ Clearing existing cities...');
      // This might not exist, so we'll continue even if it fails
      await axios.delete(`${API_BASE_URL}/cities/all`);
    } catch (error) {
      console.log('ℹ️ Could not clear existing cities (endpoint might not exist)');
    }

    // Add each city
    const addedCities = [];
    for (const city of sampleCities) {
      try {
        console.log(`📍 Adding ${city.name}...`);
        const response = await axios.post(`${API_BASE_URL}/cities/seed`, city, {
          headers: {
            'Content-Type': 'application/json'
          }
        });
        addedCities.push(response.data);
        console.log(`✅ Added ${city.name} successfully`);
      } catch (error) {
        console.error(`❌ Failed to add ${city.name}:`, error.response?.data || error.message);
      }
    }

    console.log(`\n🎉 Successfully added ${addedCities.length} cities to the database!`);
    console.log('Cities added:');
    addedCities.forEach(city => {
      console.log(`- ${city.data?.name || city.name} (${city.data?.slug || city.slug})`);
    });

  } catch (error) {
    console.error('❌ Error seeding cities:', error.message);
  }
}

// Run the seeding script
seedCitiesViaAPI();
