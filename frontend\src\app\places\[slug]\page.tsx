'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';
import {
  MapPinIcon,
  HeartIcon,
  ShareIcon,
  ClockIcon,
  CurrencyDollarIcon} from '@heroicons/react/24/solid';
import {
  HeartIcon as HeartIconOutline,
  BookmarkIcon
} from '@heroicons/react/24/outline';
import {
  BookmarkIcon as BookmarkIconSolid
} from '@heroicons/react/24/solid';
import { apiClient } from '@/lib/api';
import { Place } from '@/types';
import { PlaceBreadcrumb } from '@/components/ui/Breadcrumb';
import { Comments } from '@/components/ui/Comments';
import { DisplayStarRating } from '@/components/ui/StarRating';
import { VisitedButton } from '@/components/ui/VisitedButton';

export default function PlaceDetailPage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [place, setPlace] = useState<Place | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [actionLoading, setActionLoading] = useState<'bookmark' | 'like' | null>(null);

  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    const fetchPlace = async () => {
      try {
        setLoading(true);
        const response = await apiClient.getPlace(slug);

        if (response.success && response.data) {
          setPlace(response.data.place);

          // Fetch bookmark/like status if user is authenticated
          if (isAuthenticated && response.data.place._id) {
            try {
              const statusResponse = await apiClient.getPlaceStatus(response.data.place._id);
              if (statusResponse.success && statusResponse.data) {
                setIsBookmarked(statusResponse.data.isBookmarked);
                setIsLiked(statusResponse.data.isLiked);
              }
            } catch (statusError) {
              console.error('Error fetching place status:', statusError);
            }
          }
        } else {
          setError('Place not found');
        }
      } catch (err) {
        console.error('Error fetching place:', err);
        setError('Failed to load place details');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchPlace();
    }
  }, [slug, isAuthenticated]);

  const handleBookmark = async () => {
    if (!isAuthenticated) {
      toast.error('Please login to bookmark places');
      return;
    }

    if (!place) return;

    try {
      setActionLoading('bookmark');
      const response = await apiClient.togglePlaceBookmark(place.id);

      if (response.success) {
        setIsBookmarked(response.data.isBookmarked);
        toast.success(response.data.message);
      }
    } catch (error: any) {
      console.error('Bookmark error:', error);
      toast.error(error.message || 'Failed to update bookmark');
    } finally {
      setActionLoading(null);
    }
  };

  const handleLike = async () => {
    if (!isAuthenticated) {
      toast.error('Please login to like places');
      return;
    }

    if (!place) return;

    try {
      setActionLoading('like');
      const response = await apiClient.togglePlaceLike(place.id);

      if (response.success) {
        setIsLiked(response.data.isLiked);
        toast.success(response.data.message);
      }
    } catch (error: any) {
      console.error('Like error:', error);
      toast.error(error.message || 'Failed to update like');
    } finally {
      setActionLoading(null);
    }
  };

  const handleShare = () => {
    if (navigator.share && place) {
      navigator.share({
        title: place.name,
        text: place.description,
        url: window.location.href,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  // Simple fallback images for each category
  const getFallbackImage = (category: string) => {
    const fallbacks = {
      museum: 'https://images.unsplash.com/photo-1566139884669-4b9356b4c040?w=800&q=80',
      religious: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&q=80',
      restaurant: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=800&q=80',
      attraction: 'https://images.unsplash.com/photo-1511739001486-6bfe10ce785f?w=800&q=80',
      default: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800&q=80'
    };

    return fallbacks[category as keyof typeof fallbacks] || fallbacks.default;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-96 bg-gray-200 rounded-xl mb-8"></div>
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3 mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !place) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <MapPinIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Place Not Found</h1>
          <p className="text-gray-600 mb-6">{error || 'The place you\'re looking for doesn\'t exist.'}</p>
          <Link
            href="/places"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Browse All Places
          </Link>
        </div>
      </div>
    );
  }

  const primaryImage = place.images?.find(img => img.isPrimary) || place.images?.[0];
  const heroImageUrl = primaryImage?.url || getFallbackImage(place.category);
  const allImages = place.images?.length > 0 ? place.images : [{ url: heroImageUrl, alt: place.name, isPrimary: true }];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="relative h-96 overflow-hidden">
        <img
          src={allImages[activeImageIndex]?.url || heroImageUrl}
          alt={allImages[activeImageIndex]?.alt || place.name}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = getFallbackImage(place.category);
          }}
        />
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        
        {/* Image Navigation */}
        {allImages.length > 1 && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {allImages.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveImageIndex(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === activeImageIndex ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </div>
        )}
        
        {/* Action Buttons */}
        <div className="absolute top-4 right-4 flex space-x-2">
          <button
            onClick={handleBookmark}
            disabled={actionLoading === 'bookmark'}
            className="p-3 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title={isBookmarked ? 'Remove bookmark' : 'Bookmark this place'}
          >
            {actionLoading === 'bookmark' ? (
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-700"></div>
            ) : isBookmarked ? (
              <BookmarkIconSolid className="h-6 w-6 text-blue-600" />
            ) : (
              <BookmarkIcon className="h-6 w-6 text-gray-700" />
            )}
          </button>

          <button
            onClick={handleLike}
            disabled={actionLoading === 'like'}
            className="p-3 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title={isLiked ? 'Unlike this place' : 'Like this place'}
          >
            {actionLoading === 'like' ? (
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-700"></div>
            ) : isLiked ? (
              <HeartIcon className="h-6 w-6 text-red-500" />
            ) : (
              <HeartIconOutline className="h-6 w-6 text-gray-700" />
            )}
          </button>

          <button
            onClick={handleShare}
            className="p-3 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors"
            title="Share this place"
          >
            <ShareIcon className="h-6 w-6 text-gray-700" />
          </button>
        </div>
        
        {/* Place Info Overlay */}
        <div className="absolute inset-0 flex items-end">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8 w-full">
            <div className="text-white">
              <div className="flex items-center space-x-2 mb-2">
                <span className="px-3 py-1 bg-blue-600 text-white rounded-full text-sm font-medium capitalize">
                  {place.category}
                </span>
                {place.city && typeof place.city !== 'string' && (
                  <>
                    <MapPinIcon className="h-4 w-4" />
                    <span className="text-lg">{place.city.name}</span>
                  </>
                )}
                {place.city && typeof place.city === 'string' && (
                  <>
                    <MapPinIcon className="h-4 w-4" />
                    <span className="text-lg">{place.city}</span>
                  </>
                )}
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-4">{place.name}</h1>
              <div className="flex items-center space-x-6">
                {place.averageRating && (
                  <div className="flex items-center space-x-2">
                    <DisplayStarRating rating={place.averageRating || 0} size="md" showValue={false} />
                    <span className="text-lg font-medium">{(place.averageRating || 0).toFixed(1)}</span>
                    <span className="text-blue-200">({place.totalReviews?.toLocaleString() || 0} reviews)</span>
                  </div>
                )}
                {place.pricing?.entryFee && (
                  <div className="flex items-center space-x-1">
                    <CurrencyDollarIcon className="h-5 w-5" />
                    <span className="text-lg">
                      {place.pricing.entryFee.adult > 0 
                        ? `${place.pricing.entryFee.currency} ${place.pricing.entryFee.adult}`
                        : 'Free Entry'
                      }
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      {place.city && (
        <div className="bg-gray-50 border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <PlaceBreadcrumb 
              placeName={place.name}
              placeSlug={place.slug}
              cityName={typeof place.city === 'object' ? place.city.name : place.city}
              citySlug={typeof place.city === 'object' ? place.city.slug : ''}
              category={place.category}
            />
          </div>
        </div>
      )}

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Description */}
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">About {place.name}</h2>
              <p className="text-gray-700 text-lg leading-relaxed">{place.description}</p>
            </div>

            {/* Features */}
            {place.features && place.features.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Features & Amenities</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {place.features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2 text-gray-700">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Tags */}
            {place.tags && place.tags.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {place.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Reviews & Comments */}
            <Comments
              entityType="place"
              entityId={place.id}
              entitySlug={place.slug}
            />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Info */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Info</h3>
              <div className="space-y-3">
                {place.pricing?.entryFee && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CurrencyDollarIcon className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">Entry Fee</span>
                    </div>
                    <span className="text-sm font-medium">
                      {place.pricing.entryFee.adult > 0 
                        ? `${place.pricing.entryFee.currency} ${place.pricing.entryFee.adult}`
                        : 'Free'
                      }
                    </span>
                  </div>
                )}
                
                {place.timings.openingHours && typeof place.timings.openingHours === 'object' && (
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      <ClockIcon className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">Hours</span>
                    </div>
                    <div className="ml-6 space-y-1">
                      {Object.entries(place.timings.openingHours).map(([day, hours]) => {
                        // Handle cases where hours might be undefined or empty
                        const openTime = hours?.open || 'Not specified';
                        const closeTime = hours?.close || 'Not specified';
                        const isClosed = hours?.isClosed || false;

                        return (
                          <div key={day} className="flex justify-between text-sm font-medium">
                            <span className="capitalize">{day}</span>
                            <span>
                              {isClosed || !hours?.open || !hours?.close
                                ? 'Closed'
                                : `${openTime} - ${closeTime}`}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {place.averageRating && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <DisplayStarRating rating={place.averageRating || 0} size="sm" showValue={false} />
                      <span className="text-sm text-gray-600">Rating</span>
                    </div>
                    <span className="text-sm font-medium">{(place.averageRating || 0).toFixed(1)}/5</span>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <MapPinIcon className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">Category</span>
                  </div>
                  <span className="text-sm font-medium capitalize">{place.category}</span>
                </div>
              </div>
            </div>

            {/* Address */}
            {place.address && (
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Location</h3>
                <div className="space-y-2 text-sm text-gray-600">
                  {place.address.street && <p>{place.address.street}</p>}
                  {place.address.area && <p>{place.address.area}</p>}
                  <p>{place.address.city}, {place.address.country}</p>
                  {place.address.postalCode && <p>{place.address.postalCode}</p>}
                </div>
              </div>
            )}

            {/* Visit Tracking */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Track Your Visit</h3>
              <VisitedButton
                placeId={place.id}
                placeName={place.name}
                className="w-full justify-center"
              />
            </div>

            {/* Visit Button */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white text-center">
              <h3 className="text-lg font-semibold mb-2">Ready to Visit?</h3>
              <p className="text-blue-100 text-sm mb-4">Plan your trip to {place.name}</p>
              <button className="w-full bg-white text-blue-600 py-2 px-4 rounded-lg font-medium hover:bg-blue-50 transition-colors">
                Get Directions
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
