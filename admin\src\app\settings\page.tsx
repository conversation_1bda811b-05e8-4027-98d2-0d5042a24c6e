'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import AdminLayout from '@/components/layout/AdminLayout';
import { adminApiClient } from '@/lib/api';
import {
  CogIcon,
  ServerIcon,
  EnvelopeIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  DocumentArrowDownIcon,
  TrashIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { Tab } from '@headlessui/react';
import toast from 'react-hot-toast';

interface AppSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  contactEmail: string;
  supportEmail: string;
  socialLinks: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  seo: {
    defaultTitle: string;
    defaultDescription: string;
    defaultKeywords: string[];
  };
  features: {
    userRegistration: boolean;
    reviewModeration: boolean;
    emailNotifications: boolean;
    socialLogin: boolean;
  };
  limits: {
    maxImageSize: number;
    maxImagesPerPlace: number;
    maxReviewLength: number;
    rateLimit: number;
  };
  email: {
    provider: string;
    smtpHost?: string;
    smtpPort?: number;
    smtpUser?: string;
    smtpPassword?: string;
    fromEmail: string;
    fromName: string;
  };
  analytics: {
    googleAnalyticsId?: string;
    facebookPixelId?: string;
    hotjarId?: string;
  };
  maintenance: {
    enabled: boolean;
    message: string;
    allowedIPs: string[];
  };
}

interface SystemInfo {
  nodeVersion: string;
  platform: string;
  architecture: string;
  uptime: number;
  memoryUsage: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
  environment: string;
  timestamp: string;
}

interface BackupStatus {
  lastBackup: string;
  nextBackup: string;
  backupSize: string;
  status: string;
  autoBackupEnabled: boolean;
  retentionDays: number;
}

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function SettingsPage() {
  const { admin } = useAuth();
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [backupStatus, setBackupStatus] = useState<BackupStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testEmailAddress, setTestEmailAddress] = useState('');
  const [sendingTestEmail, setSendingTestEmail] = useState(false);

  const tabs = [
    { name: 'General', icon: CogIcon },
    { name: 'Email', icon: EnvelopeIcon },
    { name: 'Analytics', icon: ChartBarIcon },
    { name: 'Security', icon: ShieldCheckIcon },
    { name: 'System', icon: ServerIcon },
  ];

  useEffect(() => {
    fetchSettings();
    fetchSystemInfo();
    fetchBackupStatus();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await adminApiClient.getSettings();

      if (response.success) {
        setSettings(response.data);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast.error('Failed to fetch settings');
    } finally {
      setLoading(false);
    }
  };

  const fetchSystemInfo = async () => {
    try {
      const response = await adminApiClient.getSystemInfo();

      if (response.success) {
        setSystemInfo(response.data);
      }
    } catch (error) {
      console.error('Error fetching system info:', error);
    }
  };

  const fetchBackupStatus = async () => {
    try {
      const response = await adminApiClient.getBackupStatus();

      if (response.success) {
        setBackupStatus(response.data);
      }
    } catch (error) {
      console.error('Error fetching backup status:', error);
    }
  };

  const handleSaveSettings = async () => {
    if (!settings) return;

    try {
      setSaving(true);
      const response = await adminApiClient.updateSettings(settings);

      if (response.success) {
        toast.success('Settings saved successfully');
        setSettings(response.data);
      } else {
        throw new Error(response.error || 'Failed to save settings');
      }
    } catch (error: any) {
      console.error('Error saving settings:', error);
      toast.error(error.message || 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const handleTestEmail = async () => {
    if (!testEmailAddress) {
      toast.error('Please enter an email address');
      return;
    }

    try {
      setSendingTestEmail(true);
      const response = await adminApiClient.testEmailConfig(testEmailAddress);

      if (response.success) {
        toast.success('Test email sent successfully');
      } else {
        throw new Error(response.error || 'Failed to send test email');
      }
    } catch (error: any) {
      console.error('Error sending test email:', error);
      toast.error(error.message || 'Failed to send test email');
    } finally {
      setSendingTestEmail(false);
    }
  };

  const handleClearCache = async () => {
    if (!confirm('Are you sure you want to clear the cache?')) return;

    try {
      const response = await adminApiClient.clearCache();

      if (response.success) {
        toast.success('Cache cleared successfully');
      } else {
        throw new Error(response.error || 'Failed to clear cache');
      }
    } catch (error: any) {
      console.error('Error clearing cache:', error);
      toast.error(error.message || 'Failed to clear cache');
    }
  };

  const handleExportData = async () => {
    try {
      const response = await adminApiClient.exportData();

      if (response.success) {
        toast.success('Data export initiated');
      } else {
        throw new Error(response.error || 'Failed to export data');
      }
    } catch (error: any) {
      console.error('Error exporting data:', error);
      toast.error(error.message || 'Failed to export data');
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  if (loading || !settings) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage your application settings and configuration
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              onClick={handleSaveSettings}
              disabled={saving}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400"
            >
              {saving ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </div>

        {/* Settings Tabs */}
        <div className="bg-white shadow rounded-lg">
          <Tab.Group>
            <Tab.List className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                {tabs.map((tab) => (
                  <Tab
                    key={tab.name}
                    className={({ selected }) =>
                      classNames(
                        selected
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                        'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center'
                      )
                    }
                  >
                    <tab.icon className="h-5 w-5 mr-2" />
                    {tab.name}
                  </Tab>
                ))}
              </nav>
            </Tab.List>

            <Tab.Panels>
              {/* General Settings */}
              <Tab.Panel className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg leading-6 font-medium text-gray-900">General Settings</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Basic information about your application
                    </p>
                  </div>

                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Site Name</label>
                      <input
                        type="text"
                        value={settings.siteName}
                        onChange={(e) => setSettings({ ...settings, siteName: e.target.value })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Site URL</label>
                      <input
                        type="url"
                        value={settings.siteUrl}
                        onChange={(e) => setSettings({ ...settings, siteUrl: e.target.value })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div className="sm:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Site Description</label>
                      <textarea
                        rows={3}
                        value={settings.siteDescription}
                        onChange={(e) => setSettings({ ...settings, siteDescription: e.target.value })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Contact Email</label>
                      <input
                        type="email"
                        value={settings.contactEmail}
                        onChange={(e) => setSettings({ ...settings, contactEmail: e.target.value })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Support Email</label>
                      <input
                        type="email"
                        value={settings.supportEmail}
                        onChange={(e) => setSettings({ ...settings, supportEmail: e.target.value })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>

                  {/* Features */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">Features</h4>
                    <div className="space-y-4">
                      {Object.entries(settings.features).map(([key, value]) => (
                        <div key={key} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => setSettings({
                              ...settings,
                              features: { ...settings.features, [key]: e.target.checked }
                            })}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <label className="ml-2 block text-sm text-gray-900">
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Limits */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">Limits</h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Max Image Size (MB)</label>
                        <input
                          type="number"
                          value={settings.limits.maxImageSize}
                          onChange={(e) => setSettings({
                            ...settings,
                            limits: { ...settings.limits, maxImageSize: parseInt(e.target.value) }
                          })}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">Max Images Per Place</label>
                        <input
                          type="number"
                          value={settings.limits.maxImagesPerPlace}
                          onChange={(e) => setSettings({
                            ...settings,
                            limits: { ...settings.limits, maxImagesPerPlace: parseInt(e.target.value) }
                          })}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">Max Review Length</label>
                        <input
                          type="number"
                          value={settings.limits.maxReviewLength}
                          onChange={(e) => setSettings({
                            ...settings,
                            limits: { ...settings.limits, maxReviewLength: parseInt(e.target.value) }
                          })}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">Rate Limit (requests/min)</label>
                        <input
                          type="number"
                          value={settings.limits.rateLimit}
                          onChange={(e) => setSettings({
                            ...settings,
                            limits: { ...settings.limits, rateLimit: parseInt(e.target.value) }
                          })}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </Tab.Panel>

              {/* Email Settings */}
              <Tab.Panel className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Email Configuration</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Configure email settings for notifications and communications
                    </p>
                  </div>

                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email Provider</label>
                      <select
                        value={settings.email.provider}
                        onChange={(e) => setSettings({
                          ...settings,
                          email: { ...settings.email, provider: e.target.value }
                        })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="smtp">SMTP</option>
                        <option value="sendgrid">SendGrid</option>
                        <option value="mailgun">Mailgun</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">From Email</label>
                      <input
                        type="email"
                        value={settings.email.fromEmail}
                        onChange={(e) => setSettings({
                          ...settings,
                          email: { ...settings.email, fromEmail: e.target.value }
                        })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">From Name</label>
                      <input
                        type="text"
                        value={settings.email.fromName}
                        onChange={(e) => setSettings({
                          ...settings,
                          email: { ...settings.email, fromName: e.target.value }
                        })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    {settings.email.provider === 'smtp' && (
                      <>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">SMTP Host</label>
                          <input
                            type="text"
                            value={settings.email.smtpHost || ''}
                            onChange={(e) => setSettings({
                              ...settings,
                              email: { ...settings.email, smtpHost: e.target.value }
                            })}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700">SMTP Port</label>
                          <input
                            type="number"
                            value={settings.email.smtpPort || ''}
                            onChange={(e) => setSettings({
                              ...settings,
                              email: { ...settings.email, smtpPort: parseInt(e.target.value) }
                            })}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700">SMTP Username</label>
                          <input
                            type="text"
                            value={settings.email.smtpUser || ''}
                            onChange={(e) => setSettings({
                              ...settings,
                              email: { ...settings.email, smtpUser: e.target.value }
                            })}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700">SMTP Password</label>
                          <input
                            type="password"
                            value={settings.email.smtpPassword || ''}
                            onChange={(e) => setSettings({
                              ...settings,
                              email: { ...settings.email, smtpPassword: e.target.value }
                            })}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      </>
                    )}
                  </div>

                  {/* Test Email */}
                  <div className="border-t pt-6">
                    <h4 className="text-md font-medium text-gray-900 mb-4">Test Email Configuration</h4>
                    <div className="flex space-x-4">
                      <div className="flex-1">
                        <input
                          type="email"
                          value={testEmailAddress}
                          onChange={(e) => setTestEmailAddress(e.target.value)}
                          placeholder="Enter email address to test"
                          className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <button
                        onClick={handleTestEmail}
                        disabled={sendingTestEmail || !testEmailAddress}
                        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400"
                      >
                        {sendingTestEmail ? 'Sending...' : 'Send Test Email'}
                      </button>
                    </div>
                  </div>
                </div>
              </Tab.Panel>

              {/* Analytics Settings */}
              <Tab.Panel className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Analytics & Tracking</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Configure analytics and tracking services
                    </p>
                  </div>

                  <div className="grid grid-cols-1 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Google Analytics ID</label>
                      <input
                        type="text"
                        value={settings.analytics.googleAnalyticsId || ''}
                        onChange={(e) => setSettings({
                          ...settings,
                          analytics: { ...settings.analytics, googleAnalyticsId: e.target.value }
                        })}
                        placeholder="G-XXXXXXXXXX"
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Facebook Pixel ID</label>
                      <input
                        type="text"
                        value={settings.analytics.facebookPixelId || ''}
                        onChange={(e) => setSettings({
                          ...settings,
                          analytics: { ...settings.analytics, facebookPixelId: e.target.value }
                        })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Hotjar ID</label>
                      <input
                        type="text"
                        value={settings.analytics.hotjarId || ''}
                        onChange={(e) => setSettings({
                          ...settings,
                          analytics: { ...settings.analytics, hotjarId: e.target.value }
                        })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
              </Tab.Panel>

              {/* Security Settings */}
              <Tab.Panel className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Security & Maintenance</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Configure security settings and maintenance mode
                    </p>
                  </div>

                  {/* Maintenance Mode */}
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                      </div>
                      <div className="ml-3 flex-1">
                        <h4 className="text-sm font-medium text-yellow-800">Maintenance Mode</h4>
                        <div className="mt-2">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              checked={settings.maintenance.enabled}
                              onChange={(e) => setSettings({
                                ...settings,
                                maintenance: { ...settings.maintenance, enabled: e.target.checked }
                              })}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label className="ml-2 block text-sm text-yellow-800">
                              Enable maintenance mode
                            </label>
                          </div>
                          <div className="mt-3">
                            <textarea
                              rows={2}
                              value={settings.maintenance.message}
                              onChange={(e) => setSettings({
                                ...settings,
                                maintenance: { ...settings.maintenance, message: e.target.value }
                              })}
                              placeholder="Maintenance message to display to users"
                              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Backup Status */}
                  {backupStatus && (
                    <div className="bg-green-50 border border-green-200 rounded-md p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <CheckCircleIcon className="h-5 w-5 text-green-400" />
                        </div>
                        <div className="ml-3">
                          <h4 className="text-sm font-medium text-green-800">Backup Status</h4>
                          <div className="mt-2 text-sm text-green-700">
                            <p>Status: <span className="font-medium">{backupStatus.status}</span></p>
                            <p>Last Backup: {new Date(backupStatus.lastBackup).toLocaleString()}</p>
                            <p>Next Backup: {new Date(backupStatus.nextBackup).toLocaleString()}</p>
                            <p>Backup Size: {backupStatus.backupSize}</p>
                            <p>Auto Backup: {backupStatus.autoBackupEnabled ? 'Enabled' : 'Disabled'}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <button
                      onClick={handleClearCache}
                      className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <TrashIcon className="mr-2 h-4 w-4" />
                      Clear Cache
                    </button>

                    <button
                      onClick={handleExportData}
                      className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <DocumentArrowDownIcon className="mr-2 h-4 w-4" />
                      Export Data
                    </button>
                  </div>
                </div>
              </Tab.Panel>

              {/* System Information */}
              <Tab.Panel className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg leading-6 font-medium text-gray-900">System Information</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      View system status and performance metrics
                    </p>
                  </div>

                  {systemInfo && (
                    <div className="bg-gray-50 rounded-lg p-6">
                      <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Node.js Version</dt>
                          <dd className="mt-1 text-sm text-gray-900">{systemInfo.nodeVersion}</dd>
                        </div>

                        <div>
                          <dt className="text-sm font-medium text-gray-500">Platform</dt>
                          <dd className="mt-1 text-sm text-gray-900">{systemInfo.platform}</dd>
                        </div>

                        <div>
                          <dt className="text-sm font-medium text-gray-500">Architecture</dt>
                          <dd className="mt-1 text-sm text-gray-900">{systemInfo.architecture}</dd>
                        </div>

                        <div>
                          <dt className="text-sm font-medium text-gray-500">Environment</dt>
                          <dd className="mt-1 text-sm text-gray-900">{systemInfo.environment}</dd>
                        </div>

                        <div>
                          <dt className="text-sm font-medium text-gray-500">Uptime</dt>
                          <dd className="mt-1 text-sm text-gray-900">{formatUptime(systemInfo.uptime)}</dd>
                        </div>

                        <div>
                          <dt className="text-sm font-medium text-gray-500">Memory Usage</dt>
                          <dd className="mt-1 text-sm text-gray-900">
                            {formatBytes(systemInfo.memoryUsage.heapUsed)} / {formatBytes(systemInfo.memoryUsage.heapTotal)}
                          </dd>
                        </div>

                        <div className="sm:col-span-2">
                          <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                          <dd className="mt-1 text-sm text-gray-900">
                            {new Date(systemInfo.timestamp).toLocaleString()}
                          </dd>
                        </div>
                      </dl>
                    </div>
                  )}

                  <div className="flex justify-end">
                    <button
                      onClick={fetchSystemInfo}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      Refresh System Info
                    </button>
                  </div>
                </div>
              </Tab.Panel>
            </Tab.Panels>
          </Tab.Group>
        </div>
      </div>
    </AdminLayout>
  );
}