# HeritEdge Docker Images Build Script (PowerShell)
Write-Host "🚀 Building HeritEdge Docker Images..." -ForegroundColor Blue

# Function to print colored output
function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if Docker is running
try {
    docker info | Out-Null
    Write-Status "Docker is running ✓"
} catch {
    Write-Error "Docker is not running. Please start Docker and try again."
    exit 1
}

# Build backend image
Write-Status "Building backend image..."
Set-Location backend
try {
    docker build -t heritedge/backend:latest .
    Write-Success "Backend image built successfully"
} catch {
    Write-Error "Failed to build backend image"
    exit 1
}
Set-Location ..

# Build frontend image
Write-Status "Building frontend image..."
Set-Location frontend
try {
    docker build -t heritedge/frontend:latest .
    Write-Success "Frontend image built successfully"
} catch {
    Write-Error "Failed to build frontend image"
    exit 1
}
Set-Location ..

# Build admin image
Write-Status "Building admin image..."
Set-Location admin
try {
    docker build -t heritedge/admin:latest .
    Write-Success "Admin image built successfully"
} catch {
    Write-Error "Failed to build admin image"
    exit 1
}
Set-Location ..

# List built images
Write-Status "Built images:"
docker images | Select-String "heritedge"

Write-Success "All images built successfully! 🎉"
Write-Status "You can now deploy to Kubernetes using: .\scripts\deploy-k8s.ps1"
