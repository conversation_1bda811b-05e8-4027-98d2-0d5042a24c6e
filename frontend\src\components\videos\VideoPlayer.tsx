'use client';

import { useState } from 'react';
import { EyeIcon, CalendarIcon, UserIcon } from '@heroicons/react/24/outline';

interface Video {
  _id: string;
  title: string;
  description?: string;
  youtubeVideoId: string;
  thumbnailUrl: string;
  embedUrl: string;
  submittedBy: {
    _id: string;
    name: string;
  };
  reviewDate: string;
  tags: string[];
  viewCount: number;
}

interface VideoPlayerProps {
  video: Video;
  onViewIncrement?: (videoId: string) => void;
}

export default function VideoPlayer({ video, onViewIncrement }: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasIncrementedView, setHasIncrementedView] = useState(false);

  const handlePlay = async () => {
    setIsPlaying(true);
    
    // Increment view count only once per session
    if (!hasIncrementedView && onViewIncrement) {
      try {
        await fetch(`/api/videos/${video._id}/view`, {
          method: 'POST'
        });
        onViewIncrement(video._id);
        setHasIncrementedView(true);
      } catch (error) {
        console.error('Failed to increment view count:', error);
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Video Player */}
      <div className="relative aspect-video bg-gray-900">
        {!isPlaying ? (
          <div 
            className="relative w-full h-full cursor-pointer group"
            onClick={handlePlay}
          >
            <img
              src={video.thumbnailUrl}
              alt={video.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center group-hover:bg-opacity-40 transition-all">
              <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                <svg className="w-6 h-6 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
            </div>
          </div>
        ) : (
          <iframe
            src={`${video.embedUrl}?autoplay=1`}
            title={video.title}
            className="w-full h-full"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        )}
      </div>

      {/* Video Info */}
      <div className="p-4">
        <h3 className="font-semibold text-lg text-gray-900 mb-2 line-clamp-2">
          {video.title}
        </h3>

        {video.description && (
          <p className="text-gray-600 text-sm mb-3 line-clamp-3">
            {video.description}
          </p>
        )}

        {/* Video Meta */}
        <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <UserIcon className="h-4 w-4" />
              <span>{video.submittedBy.name}</span>
            </div>
            <div className="flex items-center space-x-1">
              <CalendarIcon className="h-4 w-4" />
              <span>{formatDate(video.reviewDate)}</span>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <EyeIcon className="h-4 w-4" />
            <span>{video.viewCount.toLocaleString()} views</span>
          </div>
        </div>

        {/* Tags */}
        {video.tags && video.tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {video.tags.map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
              >
                #{tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
