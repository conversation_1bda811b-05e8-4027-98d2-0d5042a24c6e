'use client';

import { VisitedPlaceFilters } from '@/types';

interface VisitedPlacesFiltersProps {
  filters: VisitedPlaceFilters;
  onFilterChange: (filters: Partial<VisitedPlaceFilters>) => void;
}

export function VisitedPlacesFilters({ filters, onFilterChange }: VisitedPlacesFiltersProps) {
  const categories = [
    'Museum',
    'Restaurant',
    'Attraction',
    'Park',
    'Religious Site',
    'Shopping',
    'Entertainment',
    'Historical Site',
    'Beach',
    'Mountain',
    'Other'
  ];

  const handleCategoryChange = (category: string) => {
    onFilterChange({ 
      category: filters.category === category ? '' : category 
    });
  };

  const handleRatingChange = (rating: number) => {
    onFilterChange({ 
      rating: filters.rating === rating ? undefined : rating 
    });
  };

  const handleSortChange = (sortBy: string, sortOrder: string) => {
    onFilterChange({ sortBy: sortBy as any, sortOrder: sortOrder as any });
  };

  const clearFilters = () => {
    onFilterChange({
      search: '',
      category: '',
      rating: undefined,
      sortBy: 'visitDate',
      sortOrder: 'desc'
    });
  };

  return (
    <div className="space-y-6">
      {/* Category Filter */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Filter by Category
        </label>
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => handleCategoryChange(category)}
              className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                filters.category === category
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Rating Filter */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Filter by My Rating
        </label>
        <div className="flex space-x-2">
          {[1, 2, 3, 4, 5].map((rating) => (
            <button
              key={rating}
              onClick={() => handleRatingChange(rating)}
              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                filters.rating === rating
                  ? 'bg-yellow-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {rating} ⭐
            </button>
          ))}
        </div>
      </div>

      {/* Sort Options */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Sort By
        </label>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <select
            value={filters.sortBy || 'visitDate'}
            onChange={(e) => handleSortChange(e.target.value, filters.sortOrder || 'desc')}
            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="visitDate">Visit Date</option>
            <option value="rating">My Rating</option>
            <option value="name">Place Name</option>
          </select>
          
          <select
            value={filters.sortOrder || 'desc'}
            onChange={(e) => handleSortChange(filters.sortBy || 'visitDate', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="desc">Newest First</option>
            <option value="asc">Oldest First</option>
          </select>

          <select
            value={filters.limit || 20}
            onChange={(e) => onFilterChange({ limit: parseInt(e.target.value) })}
            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value={10}>10 per page</option>
            <option value={20}>20 per page</option>
            <option value={50}>50 per page</option>
            <option value={100}>100 per page</option>
          </select>
        </div>
      </div>

      {/* Clear Filters */}
      <div className="flex justify-end">
        <button
          onClick={clearFilters}
          className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 transition-colors"
        >
          Clear All Filters
        </button>
      </div>
    </div>
  );
}
