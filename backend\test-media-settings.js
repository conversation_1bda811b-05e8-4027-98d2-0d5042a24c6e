const axios = require('axios');

async function testMediaAndSettingsEndpoints() {
  try {
    console.log('🧪 Testing admin login...');
    
    // Test admin login
    const loginResponse = await axios.post('http://localhost:5001/api/admin/auth/login', {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Admin login successful');
      const token = loginResponse.data.data.token;
      const headers = { Authorization: `Bearer ${token}` };
      
      // Test Media Endpoints
      console.log('\n📁 Testing Media Endpoints...');
      
      // Test get media files
      try {
        const mediaResponse = await axios.get('http://localhost:5001/api/admin/media', { headers });
        console.log('✅ Get media files:', mediaResponse.data.success ? 'SUCCESS' : 'FAILED');
        if (mediaResponse.data.success) {
          console.log('   📊 Files found:', mediaResponse.data.data.items.length);
        }
      } catch (error) {
        console.log('❌ Get media files failed:', error.response?.data?.error || error.message);
      }
      
      // Test get media folders
      try {
        const foldersResponse = await axios.get('http://localhost:5001/api/admin/media/folders', { headers });
        console.log('✅ Get media folders:', foldersResponse.data.success ? 'SUCCESS' : 'FAILED');
        if (foldersResponse.data.success) {
          console.log('   📁 Folders found:', foldersResponse.data.data.length);
        }
      } catch (error) {
        console.log('❌ Get media folders failed:', error.response?.data?.error || error.message);
      }
      
      // Test get media stats
      try {
        const statsResponse = await axios.get('http://localhost:5001/api/admin/media/stats', { headers });
        console.log('✅ Get media stats:', statsResponse.data.success ? 'SUCCESS' : 'FAILED');
        if (statsResponse.data.success) {
          console.log('   📊 Total files:', statsResponse.data.data.totalFiles);
          console.log('   📊 Total size:', statsResponse.data.data.totalSize, 'bytes');
        }
      } catch (error) {
        console.log('❌ Get media stats failed:', error.response?.data?.error || error.message);
      }
      
      // Test create folder
      try {
        const createFolderResponse = await axios.post('http://localhost:5001/api/admin/media/folders', 
          { name: 'test-folder-' + Date.now() }, 
          { headers }
        );
        console.log('✅ Create media folder:', createFolderResponse.data.success ? 'SUCCESS' : 'FAILED');
      } catch (error) {
        console.log('❌ Create media folder failed:', error.response?.data?.error || error.message);
      }
      
      // Test Settings Endpoints
      console.log('\n⚙️ Testing Settings Endpoints...');
      
      // Test get settings
      try {
        const settingsResponse = await axios.get('http://localhost:5001/api/admin/settings', { headers });
        console.log('✅ Get settings:', settingsResponse.data.success ? 'SUCCESS' : 'FAILED');
        if (settingsResponse.data.success) {
          console.log('   🏢 Site name:', settingsResponse.data.data.siteName);
          console.log('   📧 Contact email:', settingsResponse.data.data.contactEmail);
        }
      } catch (error) {
        console.log('❌ Get settings failed:', error.response?.data?.error || error.message);
      }
      
      // Test get system info
      try {
        const systemResponse = await axios.get('http://localhost:5001/api/admin/settings/system', { headers });
        console.log('✅ Get system info:', systemResponse.data.success ? 'SUCCESS' : 'FAILED');
        if (systemResponse.data.success) {
          console.log('   💻 Node version:', systemResponse.data.data.nodeVersion);
          console.log('   🖥️ Platform:', systemResponse.data.data.platform);
          console.log('   ⏱️ Uptime:', Math.floor(systemResponse.data.data.uptime / 60), 'minutes');
        }
      } catch (error) {
        console.log('❌ Get system info failed:', error.response?.data?.error || error.message);
      }
      
      // Test get backup status
      try {
        const backupResponse = await axios.get('http://localhost:5001/api/admin/settings/backup-status', { headers });
        console.log('✅ Get backup status:', backupResponse.data.success ? 'SUCCESS' : 'FAILED');
        if (backupResponse.data.success) {
          console.log('   💾 Status:', backupResponse.data.data.status);
          console.log('   📅 Last backup:', backupResponse.data.data.lastBackup);
        }
      } catch (error) {
        console.log('❌ Get backup status failed:', error.response?.data?.error || error.message);
      }
      
      // Test clear cache
      try {
        const cacheResponse = await axios.post('http://localhost:5001/api/admin/settings/clear-cache', {}, { headers });
        console.log('✅ Clear cache:', cacheResponse.data.success ? 'SUCCESS' : 'FAILED');
      } catch (error) {
        console.log('❌ Clear cache failed:', error.response?.data?.error || error.message);
      }
      
      // Test email configuration
      try {
        const emailResponse = await axios.post('http://localhost:5001/api/admin/settings/test-email', 
          { email: '<EMAIL>' }, 
          { headers }
        );
        console.log('✅ Test email:', emailResponse.data.success ? 'SUCCESS' : 'FAILED');
      } catch (error) {
        console.log('❌ Test email failed:', error.response?.data?.error || error.message);
      }
      
      console.log('\n🎉 All endpoint tests completed!');
      
    } else {
      console.error('❌ Admin login failed:', loginResponse.data);
    }
    
  } catch (error) {
    if (error.response) {
      console.error('❌ Test failed:', error.response.status, error.response.data);
    } else {
      console.error('❌ Test failed:', error.message);
    }
  }
}

testMediaAndSettingsEndpoints();
