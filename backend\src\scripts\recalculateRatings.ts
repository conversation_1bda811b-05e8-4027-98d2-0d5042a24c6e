import mongoose from 'mongoose';
import Review from '../models/Review';
import City from '../models/City';
import Place from '../models/Place';

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/heritedge');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Recalculate ratings for all cities
const recalculateCityRatings = async () => {
  console.log('🔄 Recalculating city ratings...');
  
  const cities = await City.find({});
  let updatedCount = 0;
  
  for (const city of cities) {
    const reviews = await Review.find({
      entityType: 'city',
      entityId: city._id
    });
    
    const averageRating = reviews.length > 0
      ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
      : 0;
    
    await City.findByIdAndUpdate(city._id, {
      averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
      totalReviews: reviews.length
    });
    
    console.log(`✅ Updated ${city.name}: ${averageRating.toFixed(1)} stars, ${reviews.length} reviews`);
    updatedCount++;
  }
  
  console.log(`🎉 Updated ${updatedCount} cities`);
};

// Recalculate ratings for all places
const recalculatePlaceRatings = async () => {
  console.log('🔄 Recalculating place ratings...');
  
  const places = await Place.find({});
  let updatedCount = 0;
  
  for (const place of places) {
    const reviews = await Review.find({
      entityType: 'place',
      entityId: place._id
    });
    
    const averageRating = reviews.length > 0
      ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
      : 0;
    
    await Place.findByIdAndUpdate(place._id, {
      averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
      totalReviews: reviews.length
    });
    
    console.log(`✅ Updated ${place.name}: ${averageRating.toFixed(1)} stars, ${reviews.length} reviews`);
    updatedCount++;
  }
  
  console.log(`🎉 Updated ${updatedCount} places`);
};

// Main function
const main = async () => {
  try {
    await connectDB();
    
    console.log('🚀 Starting rating recalculation...');
    
    await recalculateCityRatings();
    await recalculatePlaceRatings();
    
    console.log('✅ Rating recalculation completed successfully!');
    
    // Show summary
    const totalCities = await City.countDocuments({});
    const citiesWithReviews = await City.countDocuments({ totalReviews: { $gt: 0 } });
    const totalPlaces = await Place.countDocuments({});
    const placesWithReviews = await Place.countDocuments({ totalReviews: { $gt: 0 } });
    
    console.log('\n📊 Summary:');
    console.log(`Cities: ${citiesWithReviews}/${totalCities} have reviews`);
    console.log(`Places: ${placesWithReviews}/${totalPlaces} have reviews`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
};

// Run the script
main();
