import mongoose, { Document, Schema } from 'mongoose';

export interface ICity extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  slug: string;
  country: string;
  state?: string;
  description: string;
  overview: string;
  images: {
    url: string;
    alt: string;
    caption?: string;
    isPrimary: boolean;
  }[];
  coordinates: {
    latitude: number;
    longitude: number;
  };
  population?: number;
  area?: number; // in square kilometers
  timezone: string;
  currency: string;
  languages: string[];
  climate: {
    type: string; // tropical, temperate, arid, etc.
    bestTimeToVisit: string;
    averageTemperature: {
      summer: number;
      winter: number;
    };
  };
  transportation: {
    howToReach: {
      byAir: string;
      byRoad: string;
      byRail: string;
    };
    localTransport: string[];
    airports: {
      name: string;
      code: string;
      distance: number; // in kilometers from city center
    }[];
  };
  economy: {
    majorIndustries: string[];
    gdp?: number;
  };
  culture: {
    festivals: string[];
    traditions: string[];
    artAndCrafts: string[];
  };
  places: mongoose.Types.ObjectId[]; // References to Place model
  averageRating: number;
  totalReviews: number;
  isPublished: boolean;
  isFeatured: boolean;
  tags: string[];
  seoMetadata: {
    title: string;
    description: string;
    keywords: string[];
  };
  createdBy: mongoose.Types.ObjectId;
  lastUpdatedBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const citySchema = new Schema<ICity>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  country: {
    type: String,
    required: true,
    trim: true
  },
  state: {
    type: String,
    trim: true
  },
  description: {
    type: String,
    required: true,
    maxlength: 2000
  },
  overview: {
    type: String,
    required: true,
    maxlength: 5000
  },
  images: [{
    url: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      required: true
    },
    caption: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  coordinates: {
    latitude: {
      type: Number,
      required: true,
      min: -90,
      max: 90
    },
    longitude: {
      type: Number,
      required: true,
      min: -180,
      max: 180
    }
  },
  population: {
    type: Number,
    min: 0
  },
  area: {
    type: Number,
    min: 0
  },
  timezone: {
    type: String,
    required: true
  },
  currency: {
    type: String,
    required: true
  },
  languages: [{
    type: String,
    trim: true
  }],
  climate: {
    type: {
      type: String,
      required: true
    },
    bestTimeToVisit: {
      type: String,
      required: true
    },
    averageTemperature: {
      summer: Number,
      winter: Number
    }
  },
  transportation: {
    howToReach: {
      byAir: String,
      byRoad: String,
      byRail: String
    },
    localTransport: [String],
    airports: [{
      name: String,
      code: String,
      distance: Number
    }]
  },
  economy: {
    majorIndustries: [String],
    gdp: Number
  },
  culture: {
    festivals: [String],
    traditions: [String],
    artAndCrafts: [String]
  },
  places: [{
    type: Schema.Types.ObjectId,
    ref: 'Place'
  }],
  averageRating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  totalReviews: {
    type: Number,
    default: 0,
    min: 0
  },
  isPublished: {
    type: Boolean,
    default: false
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  seoMetadata: {
    title: {
      type: String,
      required: true,
      maxlength: 60
    },
    description: {
      type: String,
      required: true,
      maxlength: 160
    },
    keywords: [String]
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: false
  },
  lastUpdatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: false
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
citySchema.index({ slug: 1 });
citySchema.index({ country: 1 });
citySchema.index({ name: 'text', description: 'text', overview: 'text' });
citySchema.index({ coordinates: '2dsphere' });
citySchema.index({ isPublished: 1, isFeatured: 1 });
citySchema.index({ tags: 1 });
citySchema.index({ averageRating: -1 });

// Virtual for URL
citySchema.virtual('url').get(function() {
  return `/cities/${this.slug}`;
});

export default mongoose.model<ICity>('City', citySchema);
