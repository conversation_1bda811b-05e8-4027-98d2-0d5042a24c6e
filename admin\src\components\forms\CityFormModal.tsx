'use client';

import { useState, useEffect } from 'react';
import { City } from '@/types';
import Modal from '@/components/ui/Modal';
import { adminApiClient } from '@/lib/api';
import toast from 'react-hot-toast';

interface CityFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  city?: City | null;
}

interface CityFormData {
  name: string;
  country: string;
  description: string;
  overview: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  timezone: string;
  currency: string;
  languages: string[];
  isPublished: boolean;
  isFeatured: boolean;
  seoMetadata: {
    title: string;
    description: string;
    keywords: string[];
  };
}

const initialFormData: CityFormData = {
  name: '',
  country: '',
  description: '',
  overview: '',
  coordinates: {
    latitude: 0,
    longitude: 0
  },
  timezone: '',
  currency: '',
  languages: [],
  isPublished: false,
  isFeatured: false,
  seoMetadata: {
    title: '',
    description: '',
    keywords: []
  }
};

export default function CityFormModal({ isOpen, onClose, onSuccess, city }: CityFormModalProps) {
  const [formData, setFormData] = useState<CityFormData>(initialFormData);
  const [loading, setLoading] = useState(false);
  const [languageInput, setLanguageInput] = useState('');
  const [keywordInput, setKeywordInput] = useState('');

  useEffect(() => {
    if (city) {
      setFormData({
        name: city.name,
        country: city.country,
        description: city.description,
        overview: city.overview || '',
        coordinates: city.coordinates,
        timezone: city.timezone || '',
        currency: city.currency || '',
        languages: city.languages || [],
        isPublished: city.isPublished,
        isFeatured: city.isFeatured,
        seoMetadata: {
          title: city.seoMetadata?.title || '',
          description: city.seoMetadata?.description || '',
          keywords: city.seoMetadata?.keywords || []
        }
      });
    } else {
      setFormData(initialFormData);
    }
  }, [city]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof CityFormData],
          [child]: type === 'number' ? parseFloat(value) || 0 : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'number' ? parseFloat(value) || 0 : value
      }));
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleAddLanguage = () => {
    if (languageInput.trim() && !formData.languages.includes(languageInput.trim())) {
      setFormData(prev => ({
        ...prev,
        languages: [...prev.languages, languageInput.trim()]
      }));
      setLanguageInput('');
    }
  };

  const handleRemoveLanguage = (language: string) => {
    setFormData(prev => ({
      ...prev,
      languages: prev.languages.filter(l => l !== language)
    }));
  };

  const handleAddKeyword = () => {
    if (keywordInput.trim() && !formData.seoMetadata.keywords.includes(keywordInput.trim())) {
      setFormData(prev => ({
        ...prev,
        seoMetadata: {
          ...prev.seoMetadata,
          keywords: [...prev.seoMetadata.keywords, keywordInput.trim()]
        }
      }));
      setKeywordInput('');
    }
  };

  const handleRemoveKeyword = (keyword: string) => {
    setFormData(prev => ({
      ...prev,
      seoMetadata: {
        ...prev.seoMetadata,
        keywords: prev.seoMetadata.keywords.filter(k => k !== keyword)
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      let response;
      if (city) {
        response = await adminApiClient.updateCity(city._id, formData);
      } else {
        response = await adminApiClient.createCity(formData);
      }

      if (response.success) {
        toast.success(`City ${city ? 'updated' : 'created'} successfully`);
        onSuccess();
        onClose();
      } else {
        throw new Error(response.error || `Failed to ${city ? 'update' : 'create'} city`);
      }
    } catch (error) {
      console.error('Error saving city:', error);
      toast.error(`Failed to ${city ? 'update' : 'create'} city`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={city ? 'Edit City' : 'Add New City'}
      size="xl"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              City Name *
            </label>
            <input
              type="text"
              name="name"
              id="name"
              required
              value={formData.name}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label htmlFor="country" className="block text-sm font-medium text-gray-700">
              Country *
            </label>
            <input
              type="text"
              name="country"
              id="country"
              required
              value={formData.country}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700">
            Description *
          </label>
          <textarea
            name="description"
            id="description"
            rows={3}
            required
            value={formData.description}
            onChange={handleInputChange}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label htmlFor="overview" className="block text-sm font-medium text-gray-700">
            Overview
          </label>
          <textarea
            name="overview"
            id="overview"
            rows={4}
            value={formData.overview}
            onChange={handleInputChange}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Coordinates */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <label htmlFor="coordinates.latitude" className="block text-sm font-medium text-gray-700">
              Latitude *
            </label>
            <input
              type="number"
              name="coordinates.latitude"
              id="coordinates.latitude"
              step="any"
              required
              value={formData.coordinates.latitude}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label htmlFor="coordinates.longitude" className="block text-sm font-medium text-gray-700">
              Longitude *
            </label>
            <input
              type="number"
              name="coordinates.longitude"
              id="coordinates.longitude"
              step="any"
              required
              value={formData.coordinates.longitude}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Additional Info */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <label htmlFor="timezone" className="block text-sm font-medium text-gray-700">
              Timezone
            </label>
            <input
              type="text"
              name="timezone"
              id="timezone"
              value={formData.timezone}
              onChange={handleInputChange}
              placeholder="e.g., Europe/Paris"
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label htmlFor="currency" className="block text-sm font-medium text-gray-700">
              Currency
            </label>
            <input
              type="text"
              name="currency"
              id="currency"
              value={formData.currency}
              onChange={handleInputChange}
              placeholder="e.g., EUR"
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Languages */}
        <div>
          <label className="block text-sm font-medium text-gray-700">Languages</label>
          <div className="mt-1 flex">
            <input
              type="text"
              value={languageInput}
              onChange={(e) => setLanguageInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddLanguage())}
              placeholder="Add language"
              className="flex-1 border-gray-300 rounded-l-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            />
            <button
              type="button"
              onClick={handleAddLanguage}
              className="px-4 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-700 hover:bg-gray-100"
            >
              Add
            </button>
          </div>
          <div className="mt-2 flex flex-wrap gap-2">
            {formData.languages.map((language) => (
              <span
                key={language}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                {language}
                <button
                  type="button"
                  onClick={() => handleRemoveLanguage(language)}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        </div>

        {/* Status */}
        <div className="flex items-center space-x-6">
          <div className="flex items-center">
            <input
              id="isPublished"
              name="isPublished"
              type="checkbox"
              checked={formData.isPublished}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
              Published
            </label>
          </div>

          <div className="flex items-center">
            <input
              id="isFeatured"
              name="isFeatured"
              type="checkbox"
              checked={formData.isFeatured}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isFeatured" className="ml-2 block text-sm text-gray-900">
              Featured
            </label>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Saving...' : city ? 'Update City' : 'Create City'}
          </button>
        </div>
      </form>
    </Modal>
  );
}
