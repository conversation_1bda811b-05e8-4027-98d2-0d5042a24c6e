import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const testEndpoint = async () => {
  try {
    const baseURL = `http://localhost:${process.env.PORT || 5001}`;
    
    console.log('🧪 Testing admin login...');
    
    // Test admin login
    const loginResponse = await axios.post(`${baseURL}/api/admin/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Admin login successful');
      const token = loginResponse.data.data.token;
      
      // Test dashboard stats
      console.log('🧪 Testing dashboard stats...');
      const statsResponse = await axios.get(`${baseURL}/api/admin/dashboard/stats`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (statsResponse.data.success) {
        console.log('✅ Dashboard stats successful:', statsResponse.data.data);
      } else {
        console.error('❌ Dashboard stats failed:', statsResponse.data);
      }
      
      // Test dashboard activity (the failing endpoint)
      console.log('🧪 Testing dashboard activity...');
      const activityResponse = await axios.get(`${baseURL}/api/admin/dashboard/activity`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (activityResponse.data.success) {
        console.log('✅ Dashboard activity successful:', activityResponse.data.data.length, 'activities');
      } else {
        console.error('❌ Dashboard activity failed:', activityResponse.data);
      }
      
    } else {
      console.error('❌ Admin login failed:', loginResponse.data);
    }
    
  } catch (error: any) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
};

// Run the test
testEndpoint();
