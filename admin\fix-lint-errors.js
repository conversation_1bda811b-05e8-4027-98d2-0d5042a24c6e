const fs = require('fs');

// Function to fix a file
function fixFile(filePath, replacements) {
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  replacements.forEach(({ from, to }) => {
    if (content.includes(from)) {
      content = content.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
      modified = true;
      console.log(`Fixed in ${filePath}: ${from} -> ${to}`);
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated: ${filePath}`);
  }
}

// Fix all remaining lint errors
const fixes = [
  // Login page - fix any type
  {
    file: 'src/app/login/page.tsx',
    replacements: [
      { from: 'error: any', to: 'error: Error | string' }
    ]
  },
  
  // Media page - remove unused imports and variables
  {
    file: 'src/app/media/page.tsx',
    replacements: [
      { from: '  PlusIcon,', to: '' },
      { from: '  FunnelIcon,', to: '' },
      { from: '  const { admin } = useAuth();', to: '  const { } = useAuth();' },
      { from: '  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);', to: '' },
      { from: '  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");', to: '' },
      { from: 'error: any', to: 'error: Error | string' }
    ]
  },
  
  // Places page - remove unused variables and fix any types
  {
    file: 'src/app/places/page.tsx',
    replacements: [
      { from: '  const [pagination, setPagination] = useState({', to: '  const [, setPagination] = useState({' },
      { from: 'error: any', to: 'error: Error | string' }
    ]
  },
  
  // Reviews page - remove unused imports and variables
  {
    file: 'src/app/reviews/page.tsx',
    replacements: [
      { from: '  MagnifyingGlassIcon,', to: '' },
      { from: '  const { admin } = useAuth();', to: '  const { } = useAuth();' },
      { from: '  const [pagination, setPagination] = useState({', to: '  const [, setPagination] = useState({' },
      { from: 'error: any', to: 'error: Error | string' }
    ]
  },
  
  // Settings page - remove unused variables and fix any types
  {
    file: 'src/app/settings/page.tsx',
    replacements: [
      { from: '  const { admin } = useAuth();', to: '  const { } = useAuth();' },
      { from: 'error: any', to: 'error: Error | string' }
    ]
  },
  
  // Users page - remove unused imports and fix any types
  {
    file: 'src/app/users/page.tsx',
    replacements: [
      { from: '  FunnelIcon,', to: '' },
      { from: 'error: any', to: 'error: Error | string' }
    ]
  },
  
  // CityFormModal - remove unused functions
  {
    file: 'src/components/forms/CityFormModal.tsx',
    replacements: [
      { from: '  const handleAddKeyword = () => {', to: '  // const handleAddKeyword = () => {' },
      { from: '  };', to: '  // };' },
      { from: '  const handleRemoveKeyword = (index: number) => {', to: '  // const handleRemoveKeyword = (index: number) => {' }
    ]
  },
  
  // PlaceFormModal - remove unused functions
  {
    file: 'src/components/forms/PlaceFormModal.tsx',
    replacements: [
      { from: '  const handleAddAmenity = () => {', to: '  // const handleAddAmenity = () => {' },
      { from: '  const handleRemoveAmenity = (index: number) => {', to: '  // const handleRemoveAmenity = (index: number) => {' },
      { from: '  const handleAddTag = () => {', to: '  // const handleAddTag = () => {' },
      { from: '  const handleRemoveTag = (index: number) => {', to: '  // const handleRemoveTag = (index: number) => {' }
    ]
  },
  
  // AuthContext - remove unused variable and fix any types
  {
    file: 'src/contexts/AuthContext.tsx',
    replacements: [
      { from: '  const [loading, setLoading] = useState(true);', to: '  const [loading, setLoading] = useState(true);' },
      { from: '  const [error, setError] = useState<string | null>(null);', to: '  const [, setError] = useState<string | null>(null);' },
      { from: 'error: any', to: 'error: Error | string' }
    ]
  }
];

console.log('Fixing admin lint errors...');
fixes.forEach(({ file, replacements }) => {
  fixFile(file, replacements);
});

console.log('Done!');
