import { Response } from 'express';
import Place from '../models/Place';
import City from '../models/City';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AdminAuthRequest } from './adminAuthController';

// @desc    Get all places with pagination and filtering
// @route   GET /api/admin/places
// @access  Private (Admin)
export const getPlaces = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const search = req.query.search as string;
  const category = req.query.category as string;
  const city = req.query.city as string;
  const isPublished = req.query.isPublished as string;
  const isFeatured = req.query.isFeatured as string;
  const sortBy = req.query.sortBy as string || 'createdAt';
  const sortOrder = req.query.sortOrder as string || 'desc';

  // Build filter object
  const filter: any = {};

  if (search) {
    filter.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } }
    ];
  }

  if (category) {
    filter.category = category;
  }

  if (city) {
    filter.city = city;
  }

  if (isPublished !== undefined) {
    filter.isPublished = isPublished === 'true';
  }

  if (isFeatured !== undefined) {
    filter.isFeatured = isFeatured === 'true';
  }

  // Build sort object
  const sort: any = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // Calculate pagination
  const skip = (page - 1) * limit;

  // Get places with pagination
  const places = await Place.find(filter)
    .populate('city', 'name country')
    .sort(sort)
    .skip(skip)
    .limit(limit);

  // Get total count for pagination
  const total = await Place.countDocuments(filter);
  const pages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: {
      items: places,
      pagination: {
        page,
        limit,
        total,
        pages
      }
    }
  });
});

// @desc    Get single place
// @route   GET /api/admin/places/:id
// @access  Private (Admin)
export const getPlace = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const place = await Place.findById(req.params.id).populate('city', 'name country');

  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  res.json({
    success: true,
    data: place
  });
});

// @desc    Create new place
// @route   POST /api/admin/places
// @access  Private (Admin)
export const createPlace = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const {
    name,
    description,
    category,
    city,
    coordinates,
    address,
    website,
    phone,
    email,
    openingHours,
    priceRange,
    images,
    amenities,
    tags,
    isPublished,
    isFeatured,
    seoMetadata
  } = req.body;

  // Verify city exists
  const cityExists = await City.findById(city);
  if (!cityExists) {
    return res.status(400).json({
      success: false,
      error: 'City not found'
    });
  }

  // Check if place already exists in this city
  const existingPlace = await Place.findOne({ 
    name: { $regex: new RegExp(`^${name}$`, 'i') },
    city: city
  });

  if (existingPlace) {
    return res.status(400).json({
      success: false,
      error: 'Place already exists in this city'
    });
  }

  // Create slug
  const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

  const place = await Place.create({
    name,
    slug,
    description,
    category,
    city,
    coordinates,
    address,
    website,
    phone,
    email,
    openingHours: openingHours || {},
    priceRange,
    images: images || [],
    amenities: amenities || [],
    tags: tags || [],
    isPublished: isPublished || false,
    isFeatured: isFeatured || false,
    seoMetadata
  });

  // Populate city information
  await place.populate('city', 'name country');

  res.status(201).json({
    success: true,
    data: place
  });
});

// @desc    Update place
// @route   PUT /api/admin/places/:id
// @access  Private (Admin)
export const updatePlace = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const place = await Place.findById(req.params.id);

  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  // Verify city exists if city is being updated
  if (req.body.city) {
    const cityExists = await City.findById(req.body.city);
    if (!cityExists) {
      return res.status(400).json({
        success: false,
        error: 'City not found'
      });
    }
  }

  // Update slug if name changed
  if (req.body.name && req.body.name !== place.name) {
    req.body.slug = req.body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
  }

  const updatedPlace = await Place.findByIdAndUpdate(
    req.params.id,
    req.body,
    { new: true, runValidators: true }
  ).populate('city', 'name country');

  res.json({
    success: true,
    data: updatedPlace
  });
});

// @desc    Delete place
// @route   DELETE /api/admin/places/:id
// @access  Private (Super Admin)
export const deletePlace = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const place = await Place.findById(req.params.id);

  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  await Place.findByIdAndDelete(req.params.id);

  res.json({
    success: true,
    message: 'Place deleted successfully'
  });
});

// @desc    Toggle place published status
// @route   PATCH /api/admin/places/:id/toggle-status
// @access  Private (Admin)
export const togglePlaceStatus = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const place = await Place.findById(req.params.id);

  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  place.isPublished = !place.isPublished;
  await place.save();

  res.json({
    success: true,
    data: place,
    message: `Place ${place.isPublished ? 'published' : 'unpublished'} successfully`
  });
});

// @desc    Toggle place featured status
// @route   PATCH /api/admin/places/:id/toggle-featured
// @access  Private (Admin)
export const togglePlaceFeatured = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const place = await Place.findById(req.params.id);

  if (!place) {
    return res.status(404).json({
      success: false,
      error: 'Place not found'
    });
  }

  place.isFeatured = !place.isFeatured;
  await place.save();

  res.json({
    success: true,
    data: place,
    message: `Place ${place.isFeatured ? 'featured' : 'unfeatured'} successfully`
  });
});

// @desc    Bulk delete places
// @route   POST /api/admin/places/bulk-delete
// @access  Private (Super Admin)
export const bulkDeletePlaces = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const { ids } = req.body;

  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Please provide an array of place IDs'
    });
  }

  const result = await Place.deleteMany({ _id: { $in: ids } });

  res.json({
    success: true,
    message: `${result.deletedCount} places deleted successfully`
  });
});

// @desc    Get place statistics
// @route   GET /api/admin/places/stats
// @access  Private (Admin)
export const getPlaceStats = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const totalPlaces = await Place.countDocuments();
  const publishedPlaces = await Place.countDocuments({ isPublished: true });
  const featuredPlaces = await Place.countDocuments({ isFeatured: true });
  
  // Places created in the last 30 days
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const recentPlaces = await Place.countDocuments({ 
    createdAt: { $gte: thirtyDaysAgo } 
  });

  // Places by category
  const placesByCategory = await Place.aggregate([
    {
      $group: {
        _id: '$category',
        count: { $sum: 1 }
      }
    },
    { $sort: { count: -1 } }
  ]);

  // Places by city (top 10)
  const placesByCity = await Place.aggregate([
    {
      $lookup: {
        from: 'cities',
        localField: 'city',
        foreignField: '_id',
        as: 'cityInfo'
      }
    },
    {
      $unwind: '$cityInfo'
    },
    {
      $group: {
        _id: '$cityInfo.name',
        count: { $sum: 1 }
      }
    },
    { $sort: { count: -1 } },
    { $limit: 10 }
  ]);

  res.json({
    success: true,
    data: {
      totalPlaces,
      publishedPlaces,
      featuredPlaces,
      recentPlaces,
      placesByCategory,
      placesByCity
    }
  });
});
