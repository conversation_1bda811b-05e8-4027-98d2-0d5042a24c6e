'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  HeartIcon,
  BookmarkIcon,
  MapPinIcon,
  CalendarDaysIcon,
  EyeIcon,
  PlusIcon,
  ChartBarIcon,
  GlobeAltIcon,
  BuildingLibraryIcon,
  UserGroupIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartIconSolid,
  BookmarkIcon as BookmarkIconSolid,
  StarIcon as StarIconSolid
} from '@heroicons/react/24/solid';
import { apiClient } from '@/lib/api';
import { Breadcrumb } from '@/components/ui/Breadcrumb';

interface DashboardData {
  user: any;
  stats: {
    totalBookmarkedPlaces: number;
    totalBookmarkedCities: number;
    totalLikedPlaces: number;
    totalItineraries: number;
    totalVisitedPlaces: number;
  };
  recentItineraries: any[];
  recentBookmarks: any[];
  recentLikes: any[];
}

export default function DashboardPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!isAuthenticated) return;

      try {
        setLoading(true);
        console.log('Fetching dashboard data for user:', user?.email);
        const response = await apiClient.getDashboard();
        console.log('Dashboard API response:', response);

        if (response.success && response.data) {
          console.log('Setting dashboard data:', response.data);
          setDashboardData(response.data);
        } else {
          console.log('No dashboard data received, creating empty structure');
          // If no data, create empty dashboard structure
          setDashboardData({
            user: user,
            stats: {
              totalBookmarkedPlaces: 0,
              totalBookmarkedCities: 0,
              totalLikedPlaces: 0,
              totalItineraries: 0,
              totalVisitedPlaces: 0
            },
            recentItineraries: [],
            recentBookmarks: [],
            recentLikes: []
          });
        }
      } catch (err: any) {
        console.error('Dashboard error:', err);
        console.error('Error details:', {
          status: err.response?.status,
          data: err.response?.data,
          message: err.message
        });

        // Always create empty dashboard structure for new users
        console.log('Creating empty dashboard due to error');
        setDashboardData({
          user: user,
          stats: {
            totalBookmarkedPlaces: 0,
            totalBookmarkedCities: 0,
            totalLikedPlaces: 0,
            totalItineraries: 0,
            totalVisitedPlaces: 0
          },
          recentItineraries: [],
          recentBookmarks: [],
          recentLikes: []
        });

        // Only set error for authentication issues
        if (err.response?.status === 401) {
          setError('Please log in to view your dashboard');
        }
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated) {
      fetchDashboardData();
    }
  }, [isAuthenticated, user]);

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-xl"></div>
              ))}
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="h-64 bg-gray-200 rounded-xl"></div>
              <div className="h-64 bg-gray-200 rounded-xl"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !dashboardData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ChartBarIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Dashboard Unavailable</h1>
          <p className="text-gray-600 mb-6">{error || 'Unable to load dashboard data'}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const { stats, recentItineraries, recentBookmarks, recentLikes } = dashboardData;

  const statCards = [
    {
      title: 'Bookmarked Places',
      value: stats.totalBookmarkedPlaces,
      icon: BookmarkIconSolid,
      color: 'bg-blue-500',
      href: '/bookmarks?type=places'
    },
    {
      title: 'Liked Places',
      value: stats.totalLikedPlaces,
      icon: HeartIconSolid,
      color: 'bg-red-500',
      href: '/bookmarks?type=likes'
    },
    {
      title: 'My Itineraries',
      value: stats.totalItineraries,
      icon: CalendarDaysIcon,
      color: 'bg-green-500',
      href: '/itineraries'
    },
    {
      title: 'Places Visited',
      value: stats.totalVisitedPlaces,
      icon: MapPinIcon,
      color: 'bg-purple-500',
      href: '/visited'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex items-center space-x-4 mb-6">
            {user?.avatar ? (
              <img
                src={user.avatar}
                alt={user.name}
                className="h-16 w-16 rounded-full object-cover border-4 border-white/20"
              />
            ) : (
              <div className="h-16 w-16 bg-gradient-to-br from-amber-500 via-orange-500 to-red-500 rounded-full flex items-center justify-center border-4 border-white/20">
                <span className="text-white text-xl font-bold">
                  {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                </span>
              </div>
            )}
            <div>
              <h1 className="text-3xl md:text-4xl font-bold">
                Welcome back, {user?.name?.split(' ')[0] || 'Explorer'}!
              </h1>
              <p className="text-blue-100 text-lg">
                Continue your cultural journey and discover amazing places
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <Breadcrumb items={[{ label: 'Dashboard', isActive: true }]} />
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statCards.map((stat, index) => (
            <Link
              key={index}
              href={stat.href}
              className="bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-200 border border-gray-100 group"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${stat.color} group-hover:scale-110 transition-transform duration-200`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link
              href="/places"
              className="flex items-center space-x-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors group"
            >
              <BuildingLibraryIcon className="h-8 w-8 text-blue-600 group-hover:scale-110 transition-transform" />
              <div>
                <p className="font-medium text-gray-900">Explore Places</p>
                <p className="text-sm text-gray-600">Discover new destinations</p>
              </div>
            </Link>
            
            <Link
              href="/itineraries/new"
              className="flex items-center space-x-3 p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors group"
            >
              <PlusIcon className="h-8 w-8 text-green-600 group-hover:scale-110 transition-transform" />
              <div>
                <p className="font-medium text-gray-900">Create Itinerary</p>
                <p className="text-sm text-gray-600">Plan your next trip</p>
              </div>
            </Link>
            
            <Link
              href="/bookmarks"
              className="flex items-center space-x-3 p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors group"
            >
              <BookmarkIcon className="h-8 w-8 text-purple-600 group-hover:scale-110 transition-transform" />
              <div>
                <p className="font-medium text-gray-900">My Bookmarks</p>
                <p className="text-sm text-gray-600">View saved places</p>
              </div>
            </Link>
            
            <Link
              href="/profile"
              className="flex items-center space-x-3 p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors group"
            >
              <UserGroupIcon className="h-8 w-8 text-orange-600 group-hover:scale-110 transition-transform" />
              <div>
                <p className="font-medium text-gray-900">Edit Profile</p>
                <p className="text-sm text-gray-600">Update preferences</p>
              </div>
            </Link>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Bookmarks */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Recent Bookmarks</h3>
              <Link
                href="/bookmarks"
                className="text-sm text-blue-600 hover:text-blue-700 font-medium"
              >
                View all
              </Link>
            </div>
            {recentBookmarks.length > 0 ? (
              <div className="space-y-3">
                {recentBookmarks.slice(0, 3).map((place, index) => (
                  <Link
                    key={index}
                    href={`/places/${place.slug}`}
                    className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <img
                      src={place.images?.[0]?.url || 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400&q=80'}
                      alt={place.name}
                      className="h-12 w-12 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{place.name}</p>
                      <p className="text-sm text-gray-600">{place.city?.name}</p>
                    </div>
                    <BookmarkIconSolid className="h-5 w-5 text-blue-600" />
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <BookmarkIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-600">No bookmarks yet</p>
                <Link
                  href="/places"
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  Start exploring places
                </Link>
              </div>
            )}
          </div>

          {/* Recent Itineraries */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">My Itineraries</h3>
              <Link
                href="/itineraries"
                className="text-sm text-blue-600 hover:text-blue-700 font-medium"
              >
                View all
              </Link>
            </div>
            {recentItineraries.length > 0 ? (
              <div className="space-y-3">
                {recentItineraries.slice(0, 3).map((itinerary, index) => (
                  <Link
                    key={index}
                    href={`/itineraries/${itinerary._id}`}
                    className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <CalendarDaysIcon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{itinerary.title}</p>
                      <p className="text-sm text-gray-600">
                        {itinerary.duration} days • {itinerary.city?.name}
                      </p>
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      itinerary.status === 'published' 
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {itinerary.status}
                    </span>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <CalendarDaysIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-600">No itineraries yet</p>
                <Link
                  href="/itineraries/new"
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  Create your first itinerary
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
