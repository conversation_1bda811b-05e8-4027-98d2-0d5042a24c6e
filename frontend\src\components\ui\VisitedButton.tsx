'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api';
import { AddVisitedPlaceModal } from '@/components/visited/AddVisitedPlaceModal';
import { EditVisitedPlaceModal } from '@/components/visited/EditVisitedPlaceModal';
import { VisitedPlace } from '@/types';
import { 
  CheckCircleIcon,
  PlusCircleIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import { CheckCircleIcon as CheckCircleIconSolid } from '@heroicons/react/24/solid';
import toast from 'react-hot-toast';

interface VisitedButtonProps {
  placeId: string;
  placeName: string;
  className?: string;
}

export function VisitedButton({ placeId, placeName, className = '' }: VisitedButtonProps) {
  const { isAuthenticated } = useAuth();
  const [isVisited, setIsVisited] = useState(false);
  const [visitDetails, setVisitDetails] = useState<VisitedPlace | null>(null);
  const [loading, setLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  // Check visited status
  useEffect(() => {
    const checkVisitedStatus = async () => {
      if (!isAuthenticated) return;

      try {
        const response = await apiClient.getVisitedPlaceStatus(placeId);
        if (response.success) {
          setIsVisited(response.data.isVisited);
          setVisitDetails(response.data.visitDetails);
        }
      } catch (error) {
        console.error('Error checking visited status:', error);
      }
    };

    checkVisitedStatus();
  }, [placeId, isAuthenticated]);

  const handleClick = () => {
    if (!isAuthenticated) {
      toast.error('Please login to track visited places');
      return;
    }

    if (isVisited) {
      setShowEditModal(true);
    } else {
      setShowAddModal(true);
    }
  };

  const handlePlaceAdded = () => {
    setShowAddModal(false);
    setIsVisited(true);
    // Refresh status
    checkVisitedStatus();
    toast.success('Place added to visited list!');
  };

  const handlePlaceUpdated = () => {
    setShowEditModal(false);
    // Refresh status
    checkVisitedStatus();
    toast.success('Visit details updated!');
  };

  const checkVisitedStatus = async () => {
    if (!isAuthenticated) return;

    try {
      const response = await apiClient.getVisitedPlaceStatus(placeId);
      if (response.success) {
        setIsVisited(response.data.isVisited);
        setVisitDetails(response.data.visitDetails);
      }
    } catch (error) {
      console.error('Error checking visited status:', error);
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <button
        onClick={handleClick}
        disabled={loading}
        className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
          isVisited
            ? 'bg-green-100 text-green-700 hover:bg-green-200'
            : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
        } ${className}`}
        title={isVisited ? 'Edit visit details' : 'Mark as visited'}
      >
        {loading ? (
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current"></div>
        ) : isVisited ? (
          <>
            <CheckCircleIconSolid className="h-5 w-5" />
            <span>Visited</span>
            <PencilIcon className="h-4 w-4 opacity-60" />
          </>
        ) : (
          <>
            <PlusCircleIcon className="h-5 w-5" />
            <span>Mark as Visited</span>
          </>
        )}
      </button>

      {/* Add Modal */}
      {showAddModal && (
        <AddVisitedPlaceModal
          onClose={() => setShowAddModal(false)}
          onPlaceAdded={handlePlaceAdded}
        />
      )}

      {/* Edit Modal */}
      {showEditModal && visitDetails && (
        <EditVisitedPlaceModal
          visitedPlace={visitDetails}
          onClose={() => setShowEditModal(false)}
          onUpdate={handlePlaceUpdated}
        />
      )}
    </>
  );
}
