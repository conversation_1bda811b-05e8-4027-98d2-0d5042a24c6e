import { Response } from 'express';
import Review from '../models/Review';
import City from '../models/City';
import Place from '../models/Place';
import { asyncHandler } from '../middleware/errorHandler';
import { AdminAuthRequest } from './adminAuthController';

// @desc    Get all reviews with pagination and filtering
// @route   GET /api/admin/reviews
// @access  Private (Admin)
export const getReviews = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const entityType = req.query.entityType as string;
  const isApproved = req.query.isApproved as string;
  const isReported = req.query.isReported as string;
  const rating = req.query.rating as string;
  const sortBy = req.query.sortBy as string || 'createdAt';
  const sortOrder = req.query.sortOrder as string || 'desc';

  // Build filter object
  const filter: any = {};

  if (entityType) {
    filter.entityType = entityType;
  }

  if (isApproved !== undefined) {
    filter.isApproved = isApproved === 'true';
  }

  if (isReported !== undefined) {
    filter.isReported = isReported === 'true';
  }

  if (rating) {
    filter.rating = parseInt(rating);
  }

  // Build sort object
  const sort: any = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // Calculate pagination
  const skip = (page - 1) * limit;

  // Get reviews with pagination
  const reviews = await Review.find(filter)
    .populate('user', 'name email')
    .sort(sort)
    .skip(skip)
    .limit(limit);

  // Manually populate entityId based on entityType
  for (const review of reviews) {
    if (review.entityType === 'city') {
      const city = await City.findById(review.entityId).select('name');
      if (city) {
        (review as any).entityId = city;
      }
    } else if (review.entityType === 'place') {
      const place = await Place.findById(review.entityId).select('name');
      if (place) {
        (review as any).entityId = place;
      }
    }
  }

  // Get total count for pagination
  const total = await Review.countDocuments(filter);
  const pages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: {
      items: reviews,
      pagination: {
        page,
        limit,
        total,
        pages
      }
    }
  });
});

// @desc    Get single review
// @route   GET /api/admin/reviews/:id
// @access  Private (Admin)
export const getReview = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const review = await Review.findById(req.params.id)
    .populate('user', 'name email');

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  // Manually populate entityId based on entityType
  if (review.entityType === 'city') {
    const city = await City.findById(review.entityId).select('name');
    if (city) {
      (review as any).entityId = city;
    }
  } else if (review.entityType === 'place') {
    const place = await Place.findById(review.entityId).select('name');
    if (place) {
      (review as any).entityId = place;
    }
  }

  res.json({
    success: true,
    data: review
  });
});

// @desc    Approve review
// @route   PATCH /api/admin/reviews/:id/approve
// @access  Private (Admin)
export const approveReview = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const review = await Review.findById(req.params.id);

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  review.isApproved = true;
  review.isReported = false; // Clear reported status when approved
  await review.save();

  // Recalculate entity rating
  await recalculateEntityRating(review.entityType, review.entityId);

  res.json({
    success: true,
    data: review,
    message: 'Review approved successfully'
  });
});

// @desc    Reject review
// @route   PATCH /api/admin/reviews/:id/reject
// @access  Private (Admin)
export const rejectReview = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const review = await Review.findById(req.params.id);

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  review.isApproved = false;
  await review.save();

  // Recalculate entity rating
  await recalculateEntityRating(review.entityType, review.entityId);

  res.json({
    success: true,
    data: review,
    message: 'Review rejected successfully'
  });
});

// @desc    Delete review
// @route   DELETE /api/admin/reviews/:id
// @access  Private (Admin)
export const deleteReview = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const review = await Review.findById(req.params.id);

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  const entityType = review.entityType;
  const entityId = review.entityId;

  await Review.findByIdAndDelete(req.params.id);

  // Recalculate entity rating
  await recalculateEntityRating(entityType, entityId);

  res.json({
    success: true,
    message: 'Review deleted successfully'
  });
});

// @desc    Bulk approve reviews
// @route   POST /api/admin/reviews/bulk-approve
// @access  Private (Admin)
export const bulkApproveReviews = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const { ids } = req.body;

  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Please provide an array of review IDs'
    });
  }

  // Get reviews to recalculate ratings later
  const reviews = await Review.find({ _id: { $in: ids } });

  // Update reviews
  await Review.updateMany(
    { _id: { $in: ids } },
    { 
      isApproved: true,
      isReported: false
    }
  );

  // Recalculate ratings for affected entities
  const entityUpdates = new Map();
  reviews.forEach(review => {
    const key = `${review.entityType}-${review.entityId}`;
    entityUpdates.set(key, { type: review.entityType, id: review.entityId });
  });

  for (const entity of entityUpdates.values()) {
    await recalculateEntityRating(entity.type, entity.id);
  }

  res.json({
    success: true,
    message: `${ids.length} reviews approved successfully`
  });
});

// @desc    Bulk reject reviews
// @route   POST /api/admin/reviews/bulk-reject
// @access  Private (Admin)
export const bulkRejectReviews = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const { ids } = req.body;

  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Please provide an array of review IDs'
    });
  }

  // Get reviews to recalculate ratings later
  const reviews = await Review.find({ _id: { $in: ids } });

  // Update reviews
  await Review.updateMany(
    { _id: { $in: ids } },
    { isApproved: false }
  );

  // Recalculate ratings for affected entities
  const entityUpdates = new Map();
  reviews.forEach(review => {
    const key = `${review.entityType}-${review.entityId}`;
    entityUpdates.set(key, { type: review.entityType, id: review.entityId });
  });

  for (const entity of entityUpdates.values()) {
    await recalculateEntityRating(entity.type, entity.id);
  }

  res.json({
    success: true,
    message: `${ids.length} reviews rejected successfully`
  });
});

// @desc    Get review statistics
// @route   GET /api/admin/reviews/stats
// @access  Private (Admin)
export const getReviewStats = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const totalReviews = await Review.countDocuments();
  const approvedReviews = await Review.countDocuments({ isApproved: true });
  const pendingReviews = await Review.countDocuments({ isApproved: false });
  const reportedReviews = await Review.countDocuments({ isReported: true });
  
  // Reviews created in the last 30 days
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const recentReviews = await Review.countDocuments({ 
    createdAt: { $gte: thirtyDaysAgo } 
  });

  // Reviews by rating
  const reviewsByRating = await Review.aggregate([
    { $match: { isApproved: true } },
    {
      $group: {
        _id: '$rating',
        count: { $sum: 1 }
      }
    },
    { $sort: { _id: 1 } }
  ]);

  // Reviews by entity type
  const reviewsByType = await Review.aggregate([
    {
      $group: {
        _id: '$entityType',
        count: { $sum: 1 }
      }
    }
  ]);

  res.json({
    success: true,
    data: {
      totalReviews,
      approvedReviews,
      pendingReviews,
      reportedReviews,
      recentReviews,
      reviewsByRating,
      reviewsByType
    }
  });
});

// Helper function to recalculate entity rating
async function recalculateEntityRating(entityType: string, entityId: any) {
  try {
    // Get approved reviews for this entity
    const reviews = await Review.find({
      entityType,
      entityId,
      isApproved: true
    });

    const totalReviews = reviews.length;
    const averageRating = totalReviews > 0 
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews 
      : 0;

    // Update the entity
    if (entityType === 'city') {
      await City.findByIdAndUpdate(entityId, {
        averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
        totalReviews
      });
    } else {
      await Place.findByIdAndUpdate(entityId, {
        averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
        totalReviews
      });
    }
  } catch (error) {
    console.error('Error recalculating entity rating:', error);
  }
}
