'use client';

import { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';
import {
  MapPinIcon,

  FunnelIcon,
  ChevronDownIcon,
  GlobeAltIcon,
  BuildingLibraryIcon,
  HomeModernIcon,
  ShoppingBagIcon,
  MusicalNoteIcon,
  SparklesIcon
} from '@heroicons/react/24/solid';
import {

  MagnifyingGlassIcon,
  BookmarkIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import {
  BookmarkIcon as BookmarkIconSolid,
  HeartIcon as HeartIconSolid
} from '@heroicons/react/24/solid';
import { apiClient } from '@/lib/api';
import { City, Place } from '@/types';
import { DisplayStarRating } from '@/components/ui/StarRating';
import { PlacesBreadcrumb } from '@/components/ui/Breadcrumb';

function PlacesContent() {
  const [places, setPlaces] = useState<Place[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCity, setSelectedCity] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [totalPlaces, setTotalPlaces] = useState(0);
  const [hasMorePlaces, setHasMorePlaces] = useState(false);

  const searchParams = useSearchParams();

  // Fetch cities and places efficiently
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch cities and all places in parallel
        const [citiesResponse, placesResponse] = await Promise.all([
          apiClient.getCities(),
          apiClient.getAllPlaces({ limit: 50 }) // Reduced limit for better performance
        ]);

        // Set cities
        if (citiesResponse.success && citiesResponse.data) {
          setCities(citiesResponse.data.items || []);
        }

        // Set places
        if (placesResponse.success && placesResponse.data) {
          setPlaces(placesResponse.data.items || []);

          // Set pagination info if available
          if (placesResponse.data.pagination) {
            setTotalPlaces(placesResponse.data.pagination.total || 0);
            setHasMorePlaces(
              placesResponse.data.pagination.page < placesResponse.data.pagination.pages
            );
          }
        }

      } catch (err: any) {
        console.error('Error fetching data:', err);

        // Handle specific error cases
        if (err.response?.status === 429) {
          setError('Too many requests. Please wait a moment and try again.');
        } else {
          setError('Failed to load places data. Please try refreshing the page.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle URL parameters
  useEffect(() => {
    const city = searchParams.get('city');
    const category = searchParams.get('category');

    if (city) setSelectedCity(city);
    if (category) setSelectedCategory(category);
  }, [searchParams]);

  // Filter places based on selected filters
  const filteredPlaces = places.filter(place => {
    // City filtering - handle both string and object city references
    let matchesCity = true;
    if (selectedCity && selectedCity !== 'all') {
      if (typeof place.city === 'string') {
        matchesCity = place.city === selectedCity;
      } else if (place.city && typeof place.city === 'object') {
        matchesCity = place.city.slug === selectedCity || place.city.name?.toLowerCase() === selectedCity.toLowerCase();
      } else {
        matchesCity = false;
      }
    }

    // Category filtering
    let matchesCategory = true;
    if (selectedCategory && selectedCategory !== 'all') {
      matchesCategory = place.category === selectedCategory;
    }

    // Search filtering
    let matchesSearch = true;
    if (searchQuery && searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase();
      matchesSearch =
        place.name.toLowerCase().includes(query) ||
        (place.description && place.description.toLowerCase().includes(query)) ||
        (place.tags && place.tags.some(tag => tag.toLowerCase().includes(query)));
    }

    return matchesCity && matchesCategory && matchesSearch;
  });

  // Group places by category
  const groupedPlaces = filteredPlaces.reduce((acc, place) => {
    const category = place.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(place);
    return acc;
  }, {} as Record<string, Place[]>);

  // Get unique categories
  const categories = Array.from(new Set(places.map(place => place.category)));

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                  <div className="h-48 bg-gray-200 rounded mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <GlobeAltIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Places</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-4">
              Discover Amazing Places
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Explore cultural heritage sites, museums, restaurants, and attractions from cities around the world
            </p>
            <div className="flex justify-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-1 flex items-center space-x-4">
                <span className="text-blue-100 px-4 py-2">
                  {filteredPlaces.length} places shown
                  {totalPlaces > 0 && totalPlaces !== filteredPlaces.length && (
                    <span className="text-blue-200"> of {totalPlaces} total</span>
                  )}
                </span>
                <span className="text-blue-100 px-4 py-2">
                  {cities.length} cities available
                </span>
                {hasMorePlaces && (
                  <span className="text-orange-200 px-4 py-2 bg-orange-500/20 rounded-lg">
                    📄 More available
                  </span>
                )}
                {selectedCity !== 'all' && (
                  <span className="text-yellow-200 px-4 py-2 bg-yellow-500/20 rounded-lg">
                    📍 {selectedCity}
                  </span>
                )}
                {selectedCategory !== 'all' && (
                  <span className="text-green-200 px-4 py-2 bg-green-500/20 rounded-lg">
                    🏷️ {selectedCategory}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Breadcrumb Section */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <PlacesBreadcrumb
            city={selectedCity}
            category={selectedCategory}
            search={searchQuery}
          />
        </div>
      </div>

      {/* Filters Section */}
      <div className="bg-white border-b border-gray-200 sticky top-16 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search places..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filter Controls */}
            <div className="flex items-center space-x-4">
              {/* City Filter */}
              <div className="relative">
                <select
                  value={selectedCity}
                  onChange={(e) => setSelectedCity(e.target.value)}
                  className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Cities</option>
                  {cities.map((city) => (
                    <option key={city.slug} value={city.slug}>
                      {city.name}
                    </option>
                  ))}
                </select>
                <ChevronDownIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>

              {/* Category Filter */}
              <div className="relative">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Categories</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}s
                    </option>
                  ))}
                </select>
                <ChevronDownIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>

              {/* Filter Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              >
                <FunnelIcon className="h-4 w-4" />
                <span className="hidden sm:block">Filters</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {filteredPlaces.length === 0 ? (
          <div className="text-center py-16">
            <MapPinIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">No places found</h2>
            <p className="text-gray-600 mb-6">
              Try adjusting your filters or search terms to find more places.
            </p>
            <button
              onClick={() => {
                setSelectedCity('all');
                setSelectedCategory('all');
                setSearchQuery('');
              }}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        ) : (
          <div className="space-y-12">
            {Object.entries(groupedPlaces).map(([category, categoryPlaces]) => (
              <div key={category} className="space-y-6">
                <div className="flex items-center space-x-3">
                  <CategoryIcon category={category} className="h-6 w-6 text-blue-600" />
                  <h2 className="text-2xl font-bold text-gray-900 capitalize">
                    {category === 'attraction' ? 'Attractions' :
                     category === 'restaurant' ? 'Restaurants' :
                     category === 'museum' ? 'Museums' :
                     category === 'religious' ? 'Religious Sites' :
                     category === 'historical' ? 'Historical Sites' :
                     category}
                  </h2>
                  <span className="text-lg text-gray-500">({categoryPlaces.length})</span>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
                  {categoryPlaces.map((place) => (
                    <PlaceCard key={place.id || place.slug} place={place} />
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default function PlacesPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    }>
      <PlacesContent />
    </Suspense>
  );
}

// Place Card Component
function PlaceCard({ place }: { place: Place }) {
  const { isAuthenticated } = useAuth();
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [actionLoading, setActionLoading] = useState<'bookmark' | 'like' | null>(null);

  const primaryImage = place.images?.find(img => img.isPrimary) || place.images?.[0];

  // Fetch bookmark/like status when user is authenticated
  useEffect(() => {
    const fetchStatus = async () => {
      if (!isAuthenticated || !place.id) return;

      try {
        const response = await apiClient.getPlaceStatus(place.id);
        if (response.success && response.data) {
          setIsBookmarked(response.data.isBookmarked);
          setIsLiked(response.data.isLiked);
        }
      } catch (error) {
        console.error('Error fetching place status:', error);
      }
    };

    fetchStatus();
  }, [isAuthenticated, place.id]);

  const handleBookmark = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      toast.error('Please login to bookmark places');
      return;
    }

    if (!place.id) return;

    try {
      setActionLoading('bookmark');
      const response = await apiClient.togglePlaceBookmark(place.id);

      if (response.success) {
        setIsBookmarked(response.data.isBookmarked);
        toast.success(response.data.message);
      }
    } catch (error: any) {
      console.error('Bookmark error:', error);
      toast.error(error.message || 'Failed to update bookmark');
    } finally {
      setActionLoading(null);
    }
  };

  const handleLike = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      toast.error('Please login to like places');
      return;
    }

    if (!place.id) return;

    try {
      setActionLoading('like');
      const response = await apiClient.togglePlaceLike(place.id);

      if (response.success) {
        setIsLiked(response.data.isLiked);
        toast.success(response.data.message);
      }
    } catch (error: any) {
      console.error('Like error:', error);
      toast.error(error.message || 'Failed to update like');
    } finally {
      setActionLoading(null);
    }
  };

  // Simple fallback images for each category
  const getFallbackImage = (category: string) => {
    const fallbacks = {
      museum: 'https://images.unsplash.com/photo-1566139884669-4b9356b4c040?w=800&q=80',
      religious: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&q=80',
      restaurant: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=800&q=80',
      attraction: 'https://images.unsplash.com/photo-1511739001486-6bfe10ce785f?w=800&q=80',
      default: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800&q=80'
    };

    return fallbacks[category as keyof typeof fallbacks] || fallbacks.default;
  };

  // Get the image URL with fallback
  const getImageUrl = () => {
    if (primaryImage?.url) {
      return primaryImage.url;
    }
    return getFallbackImage(place.category);
  };

  const imageUrl = getImageUrl();

  return (
    <Link href={`/places/${place.slug}`} className="group">
      <div className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden h-full">
        <div className="relative h-48 overflow-hidden bg-gray-200">
          <img
            src={imageUrl}
            alt={primaryImage?.alt || place.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            onError={(e) => {
              // If image fails to load, use category fallback
              const target = e.target as HTMLImageElement;
              target.src = getFallbackImage(place.category);
            }}
          />
          <div className="absolute top-4 right-4 flex items-center space-x-2">
            <div className="bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
              <DisplayStarRating rating={place.averageRating || 0} size="sm" showValue={false} />
              <span className="text-xs font-medium text-gray-700">{(place.averageRating || 0).toFixed(1)}</span>
            </div>
            {isAuthenticated && (
              <div className="flex space-x-1">
                <button
                  onClick={handleBookmark}
                  disabled={actionLoading === 'bookmark'}
                  className="p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors disabled:opacity-50"
                  title={isBookmarked ? 'Remove bookmark' : 'Bookmark this place'}
                >
                  {actionLoading === 'bookmark' ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-700"></div>
                  ) : isBookmarked ? (
                    <BookmarkIconSolid className="h-4 w-4 text-blue-600" />
                  ) : (
                    <BookmarkIcon className="h-4 w-4 text-gray-700" />
                  )}
                </button>
                <button
                  onClick={handleLike}
                  disabled={actionLoading === 'like'}
                  className="p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors disabled:opacity-50"
                  title={isLiked ? 'Unlike this place' : 'Like this place'}
                >
                  {actionLoading === 'like' ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-700"></div>
                  ) : isLiked ? (
                    <HeartIconSolid className="h-4 w-4 text-red-500" />
                  ) : (
                    <HeartIcon className="h-4 w-4 text-gray-700" />
                  )}
                </button>
              </div>
            )}
          </div>
          <div className="absolute top-4 left-4 bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-medium">
            {place.category}
          </div>
        </div>

        <div className="p-6">
          <div className="flex items-start justify-between mb-2">
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-1">
              {place.name}
            </h3>
          </div>

          {/* City Info */}
          {place.city && (
            <div className="flex items-center space-x-1 mb-2">
              <MapPinIcon className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">
                {typeof place.city === 'object' && place.city !== null
                  ? place.city.name
                  : place.city}
              </span>
            </div>
          )}

          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
            {place.description}
          </p>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {place.averageRating && (
                <div className="flex items-center space-x-2">
                  <DisplayStarRating rating={place.averageRating || 0} size="sm" showValue={false} />
                  <span className="text-sm font-medium text-gray-700">{(place.averageRating || 0).toFixed(1)}</span>
                  <span className="text-sm text-gray-600">
                    ({(place.totalReviews || 0).toLocaleString()})
                  </span>
                </div>
              )}
            </div>

            {place.pricing?.entryFee && (
              <div className="text-right">
                <div className="text-lg font-semibold text-gray-900">
                  {place.pricing.entryFee.adult > 0
                    ? `${place.pricing.entryFee.currency} ${place.pricing.entryFee.adult}`
                    : 'Free'
                  }
                </div>
                <div className="text-xs text-gray-500">per person</div>
              </div>
            )}
          </div>

          {/* Features */}
          {place.features && place.features.length > 0 && (
            <div className="mt-4 flex flex-wrap gap-1">
              {place.features.slice(0, 3).map((feature, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                >
                  {feature}
                </span>
              ))}
              {place.features.length > 3 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                  +{place.features.length - 3} more
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    </Link>
  );
}

// Category icon component
function CategoryIcon({ category, className }: { category: string; className?: string }) {
  switch (category) {
    case 'attraction':
      return <SparklesIcon className={className} />;
    case 'museum':
      return <BuildingLibraryIcon className={className} />;
    case 'restaurant':
      return <HomeModernIcon className={className} />;
    case 'hotel':
      return <BuildingLibraryIcon className={className} />;
    case 'shopping':
      return <ShoppingBagIcon className={className} />;
    case 'entertainment':
      return <MusicalNoteIcon className={className} />;
    case 'nature':
      return <GlobeAltIcon className={className} />;
    case 'religious':
      return <BuildingLibraryIcon className={className} />;
    case 'historical':
      return <BuildingLibraryIcon className={className} />;
    default:
      return <MapPinIcon className={className} />;
  }
}
