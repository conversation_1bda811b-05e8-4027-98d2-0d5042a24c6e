"use client";

import { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import {
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  CalendarIcon,
  UserIcon,
  MapPinIcon,
} from "@heroicons/react/24/outline";
import adminApiClient from "@/lib/api";

interface PendingVideo {
  _id: string;
  title: string;
  description?: string;
  youtubeUrl: string;
  youtubeVideoId: string;
  thumbnailUrl: string;
  place: {
    _id: string;
    name: string;
    slug: string;
  };
  submittedBy: {
    _id: string;
    name: string;
    email: string;
  };
  submissionDate: string;
  tags: string[];
}

export default function VideoApprovalQueue() {
  const [videos, setVideos] = useState<PendingVideo[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingVideo, setProcessingVideo] = useState<string | null>(null);
  const [reviewNotes, setReviewNotes] = useState<{ [key: string]: string }>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchPendingVideos = async (page = 1) => {
    try {
      setLoading(true);

      const response = await adminApiClient.fetchPendingVideos(page.toString());

      const data = (await response.data) as {
        videos: PendingVideo[];
        pagination: { currentPage: number; totalPages: number };
        error?: string;
      };

      if (response.success) {
        setVideos(data.videos);
        setCurrentPage(data.pagination.currentPage);
        setTotalPages(data.pagination.totalPages);
      } else {
        toast.error(data.error || "Failed to load pending videos");
      }
    } catch (error) {
      console.error("Error fetching pending videos:", error);
      toast.error("Failed to load pending videos");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPendingVideos();
  }, []);

  const handleReview = async (
    videoId: string,
    status: "approved" | "rejected"
  ) => {
    try {
      setProcessingVideo(videoId);

      const response = await adminApiClient.reviewVideo(
        videoId,
        status,
        reviewNotes[videoId] || ""
      );

      //const data = await response.data;

      if (response.success) {
        toast.success(`Video ${status} successfully`);
        // Remove the video from the list
        setVideos((prev) => prev.filter((video) => video._id !== videoId));
        // Clear review notes
        setReviewNotes((prev) => {
          const updated = { ...prev };
          delete updated[videoId];
          return updated;
        });
      } else {
        toast.error(response.error || `Failed to ${status} video`);
      }
    } catch (error) {
      console.error(`Error ${status} video:`, error);
      toast.error(`Failed to ${status} video`);
    } finally {
      setProcessingVideo(null);
    }
  };

  const handleNotesChange = (videoId: string, notes: string) => {
    setReviewNotes((prev) => ({ ...prev, [videoId]: notes }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Video Approval Queue
          </h1>
          <p className="text-gray-600 mt-1">
            {videos.length} video{videos.length !== 1 ? "s" : ""} pending review
          </p>
        </div>
      </div>

      {videos.length > 0 ? (
        <div className="space-y-6">
          {videos.map((video) => (
            <div
              key={video._id}
              className="bg-white rounded-lg shadow-md overflow-hidden"
            >
              <div className="p-6">
                <div className="flex items-start space-x-6">
                  {/* Video Thumbnail */}
                  <div className="flex-shrink-0">
                    <div className="relative">
                      <img
                        src={video.thumbnailUrl}
                        alt={video.title}
                        className="w-48 h-28 object-cover rounded-lg"
                      />
                      <a
                        href={video.youtubeUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity rounded-lg"
                      >
                        <EyeIcon className="h-8 w-8 text-white" />
                      </a>
                    </div>
                  </div>

                  {/* Video Details */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {video.title}
                    </h3>

                    {video.description && (
                      <p className="text-gray-600 mb-3 line-clamp-2">
                        {video.description}
                      </p>
                    )}

                    {/* Meta Information */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-2">
                        <MapPinIcon className="h-4 w-4" />
                        <span>{video.place.name}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <UserIcon className="h-4 w-4" />
                        <span>{video.submittedBy.name}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CalendarIcon className="h-4 w-4" />
                        <span>{formatDate(video.submissionDate)}</span>
                      </div>
                    </div>

                    {/* Tags */}
                    {video.tags && video.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {video.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                          >
                            #{tag}
                          </span>
                        ))}
                      </div>
                    )}

                    {/* YouTube URL */}
                    <div className="mb-4">
                      <a
                        href={video.youtubeUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 text-sm break-all"
                      >
                        {video.youtubeUrl}
                      </a>
                    </div>

                    {/* Review Notes */}
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Review Notes (Optional)
                      </label>
                      <textarea
                        value={reviewNotes[video._id] || ""}
                        onChange={(e) =>
                          handleNotesChange(video._id, e.target.value)
                        }
                        placeholder="Add notes about your review decision..."
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <button
                        onClick={() => handleReview(video._id, "approved")}
                        disabled={processingVideo === video._id}
                        className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <CheckCircleIcon className="h-4 w-4" />
                        <span>
                          {processingVideo === video._id
                            ? "Processing..."
                            : "Approve"}
                        </span>
                      </button>

                      <button
                        onClick={() => handleReview(video._id, "rejected")}
                        disabled={processingVideo === video._id}
                        className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <XCircleIcon className="h-4 w-4" />
                        <span>
                          {processingVideo === video._id
                            ? "Processing..."
                            : "Reject"}
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2">
              <button
                onClick={() => fetchPendingVideos(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50"
              >
                Previous
              </button>

              <span className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </span>

              <button
                onClick={() => fetchPendingVideos(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50"
              >
                Next
              </button>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-12">
          <CheckCircleIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No pending videos
          </h3>
          <p className="text-gray-600">
            All video submissions have been reviewed. Great job!
          </p>
        </div>
      )}
    </div>
  );
}
