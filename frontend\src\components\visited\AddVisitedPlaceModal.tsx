'use client';

import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';
import { Place } from '@/types';
import { StarRating } from '@/components/ui/StarRating';
import { 
  XMarkIcon, 
  MagnifyingGlassIcon,
  MapPinIcon,
  CalendarIcon,
  StarIcon,
  ChatBubbleLeftIcon
} from '@heroicons/react/24/outline';

interface AddVisitedPlaceModalProps {
  onClose: () => void;
  onPlaceAdded: () => void;
}

export function AddVisitedPlaceModal({ onClose, onPlaceAdded }: AddVisitedPlaceModalProps) {
  const [step, setStep] = useState<'search' | 'details'>('search');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Place[]>([]);
  const [selectedPlace, setSelectedPlace] = useState<Place | null>(null);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  // Form data
  const [visitDate, setVisitDate] = useState(new Date().toISOString().split('T')[0]);
  const [rating, setRating] = useState<number>(0);
  const [review, setReview] = useState('');

  // Search for places
  const searchPlaces = async () => {
    if (!searchQuery.trim()) return;

    try {
      setLoading(true);
      const response = await apiClient.getAllPlaces({
        search: searchQuery,
        limit: 10
      });
      
      if (response.success && response.data) {
        setSearchResults(response.data.items || []);
      }
    } catch (error) {
      console.error('Error searching places:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery) {
        searchPlaces();
      } else {
        setSearchResults([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  const handlePlaceSelect = (place: Place) => {
    setSelectedPlace(place);
    setStep('details');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedPlace) return;

    try {
      setSubmitting(true);
      const response = await apiClient.addVisitedPlace({
        placeId: selectedPlace.id,
        visitDate,
        rating: rating > 0 ? rating : undefined,
        review: review.trim() || undefined
      });

      if (response.success) {
        onPlaceAdded();
      } else {
        alert(response.error || 'Failed to add place');
      }
    } catch (error: any) {
      console.error('Error adding visited place:', error);
      alert(error.message || 'Failed to add place');
    } finally {
      setSubmitting(false);
    }
  };

  const handleBackToSearch = () => {
    setStep('search');
    setSelectedPlace(null);
    setRating(0);
    setReview('');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {step === 'search' ? 'Add Visited Place' : 'Visit Details'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
          {step === 'search' ? (
            /* Search Step */
            <div className="p-6">
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search for a place
                </label>
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search places by name, city, or category..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    autoFocus
                  />
                </div>
              </div>

              {/* Search Results */}
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : searchResults.length > 0 ? (
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-gray-700">Search Results</h3>
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {searchResults.map((place) => (
                      <button
                        key={place.id}
                        onClick={() => handlePlaceSelect(place)}
                        className="w-full text-left p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                      >
                        <div className="flex items-start space-x-3">
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">{place.name}</h4>
                            <div className="flex items-center text-sm text-gray-600 mt-1">
                              <MapPinIcon className="h-4 w-4 mr-1" />
                              <span>
                                {typeof place.city === 'object' && place.city !== null
                                  ? `${place.city.name}, ${place.city.country}`
                                  : place.city || ''}
                              </span>
                            </div>
                            <span className="inline-block mt-2 px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded">
                              {place.category}
                            </span>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              ) : searchQuery && !loading ? (
                <div className="text-center py-8 text-gray-500">
                  <MapPinIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                  <p>No places found for &quot;{searchQuery}&quot;</p>
                  <p className="text-sm mt-1">Try searching with different keywords</p>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <MagnifyingGlassIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                  <p>Start typing to search for places</p>
                </div>
              )}
            </div>
          ) : (
            /* Details Step */
            <form onSubmit={handleSubmit} className="p-6">
              {/* Selected Place */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900">{selectedPlace?.name}</h3>
                    <div className="flex items-center text-sm text-gray-600 mt-1">
                      <MapPinIcon className="h-4 w-4 mr-1" />
                      <span>
                        {typeof selectedPlace?.city === 'object' && selectedPlace?.city !== null
                          ? `${selectedPlace.city.name}, ${selectedPlace.city.country}`
                          : selectedPlace?.city || ''}
                      </span>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={handleBackToSearch}
                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    Change Place
                  </button>
                </div>
              </div>

              {/* Visit Date */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <CalendarIcon className="h-4 w-4 inline mr-1" />
                  Visit Date
                </label>
                <input
                  type="date"
                  value={visitDate}
                  onChange={(e) => setVisitDate(e.target.value)}
                  max={new Date().toISOString().split('T')[0]}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              {/* Rating */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <StarIcon className="h-4 w-4 inline mr-1" />
                  Your Rating (Optional)
                </label>
                <div className="flex items-center space-x-2">
                  <StarRating
                    rating={rating}
                    onRatingChange={setRating}
                    size="lg"
                  />
                  {rating > 0 && (
                    <span className="text-sm text-gray-600">({rating}/5)</span>
                  )}
                </div>
              </div>

              {/* Review */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <ChatBubbleLeftIcon className="h-4 w-4 inline mr-1" />
                  Your Review (Optional)
                </label>
                <textarea
                  value={review}
                  onChange={(e) => setReview(e.target.value)}
                  placeholder="Share your experience at this place..."
                  rows={4}
                  maxLength={1000}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
                <div className="text-right text-xs text-gray-500 mt-1">
                  {review.length}/1000 characters
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={submitting}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting ? 'Adding...' : 'Add to Visited Places'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
