apiVersion: v1
kind: ConfigMap
metadata:
  name: heritedge-config
  namespace: heritedge
data:
  # Backend Configuration
  NODE_ENV: "production"
  PORT: "5001"
  MONGODB_URI: "****************************************************************************"
  JWT_EXPIRE: "24h"
  JWT_REFRESH_EXPIRE: "7d"
  FRONTEND_URL: "http://localhost:3000"
  ADMIN_URL: "http://localhost:3001"
  
  # Frontend Configuration
  NEXT_PUBLIC_API_URL: "http://heritedge-backend-service:5001/api"
  NEXT_PUBLIC_FRONTEND_URL: "http://localhost:3000"
  NEXTAUTH_URL: "http://localhost:3000"
  
  # Admin Configuration
  NEXT_PUBLIC_APP_NAME: "HeritEdge Admin Console"
  NEXT_PUBLIC_APP_VERSION: "1.0.0"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mongodb-init-script
  namespace: heritedge
data:
  init-mongo.js: |
    // MongoDB initialization script for HeritEdge
    print('Starting HeritEdge database initialization...');

    // Switch to the heritedge database
    db = db.getSiblingDB('heritedge');

    // Create collections
    db.createCollection('users');
    db.createCollection('admins');
    db.createCollection('cities');
    db.createCollection('places');
    db.createCollection('reviews');
    db.createCollection('itineraries');

    // Create indexes for better performance
    print('Creating indexes...');

    // Users indexes
    db.users.createIndex({ email: 1 }, { unique: true });
    db.users.createIndex({ googleId: 1 }, { sparse: true });
    db.users.createIndex({ createdAt: 1 });

    // Admins indexes
    db.admins.createIndex({ email: 1 }, { unique: true });
    db.admins.createIndex({ role: 1 });

    // Cities indexes
    db.cities.createIndex({ name: 1 });
    db.cities.createIndex({ slug: 1 }, { unique: true });
    db.cities.createIndex({ country: 1 });
    db.cities.createIndex({ isActive: 1 });

    // Places indexes
    db.places.createIndex({ name: 1 });
    db.places.createIndex({ slug: 1 }, { unique: true });
    db.places.createIndex({ city: 1 });
    db.places.createIndex({ category: 1 });
    db.places.createIndex({ isActive: 1 });
    db.places.createIndex({ location: '2dsphere' });

    // Reviews indexes
    db.reviews.createIndex({ entityType: 1, entityId: 1 });
    db.reviews.createIndex({ user: 1 });
    db.reviews.createIndex({ entityType: 1, entityId: 1, user: 1 }, { unique: true });
    db.reviews.createIndex({ isApproved: 1 });
    db.reviews.createIndex({ isReported: 1 });
    db.reviews.createIndex({ createdAt: 1 });

    // Itineraries indexes
    db.itineraries.createIndex({ user: 1 });
    db.itineraries.createIndex({ city: 1 });
    db.itineraries.createIndex({ isPublic: 1 });
    db.itineraries.createIndex({ createdAt: 1 });

    // Create default admin user (password: admin123456)
    print('Creating default admin user...');
    db.admins.insertOne({
      name: 'System Administrator',
      email: '<EMAIL>',
      password: '$2b$10$8K1p/a0dclxKoNqIfrHb2eIiGQExd6yvAoK8WMnbp7f2kJHNe8ppe',
      role: 'super_admin',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    print('HeritEdge database initialization completed successfully!');
    print('Default admin credentials:');
    print('Email: <EMAIL>');
    print('Password: admin123456');
