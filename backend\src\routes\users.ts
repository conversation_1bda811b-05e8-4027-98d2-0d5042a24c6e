import express from 'express';
import { query, param, body } from 'express-validator';
import {
  getUserProfile,
  getUserBookmarks,
  getUserContributions,
  updateUserPreferences,
  getDashboard,
  togglePlaceBookmark,
  togglePlaceLike,
  toggleCityBookmark,
  getPlaceStatus,
  getCityStatus,
  getVisitedPlaces,
  addVisitedPlace,
  updateVisitedPlace,
  removeVisitedPlace,
  getVisitedPlaceStatus
} from '../controllers/userController';
import { protect, authorize } from '../middleware/auth';
import { handleValidationErrors } from '../middleware/validation';

const router = express.Router();

// @route   GET /api/users/dashboard
// @desc    Get user dashboard data
// @access  Private
router.get('/dashboard', protect, getDashboard);

// @route   GET /api/users/visited-places
// @desc    Get user's visited places with filtering and pagination
// @access  Private
router.get('/visited-places', protect, [
  query('page')
    .optional()
    .toInt()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .toInt()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('sortBy')
    .optional()
    .isIn(['visitDate', 'rating', 'name'])
    .withMessage('Sort by must be visitDate, rating, or name'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
  query('category')
    .optional()
    .isString()
    .withMessage('Category must be a string'),
  query('rating')
    .optional()
    .toInt()
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  query('search')
    .optional()
    .isString()
    .withMessage('Search must be a string')
], handleValidationErrors, getVisitedPlaces);

// @route   POST /api/users/visited-places
// @desc    Add a place to visited places
// @access  Private
router.post('/visited-places', protect, [
  body('placeId')
    .isMongoId()
    .withMessage('Invalid place ID'),
  body('visitDate')
    .isISO8601()
    .withMessage('Visit date must be a valid date'),
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('review')
    .optional()
    .isString()
    .isLength({ max: 1000 })
    .withMessage('Review must be a string with max 1000 characters')
], handleValidationErrors, addVisitedPlace);

// @route   PUT /api/users/visited-places/:placeId
// @desc    Update a visited place
// @access  Private
router.put('/visited-places/:placeId', protect, [
  param('placeId')
    .isMongoId()
    .withMessage('Invalid place ID'),
  body('visitDate')
    .optional()
    .isISO8601()
    .withMessage('Visit date must be a valid date'),
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('review')
    .optional()
    .isString()
    .isLength({ max: 1000 })
    .withMessage('Review must be a string with max 1000 characters')
], handleValidationErrors, updateVisitedPlace);

// @route   DELETE /api/users/visited-places/:placeId
// @desc    Remove a place from visited places
// @access  Private
router.delete('/visited-places/:placeId', protect, [
  param('placeId')
    .isMongoId()
    .withMessage('Invalid place ID')
], handleValidationErrors, removeVisitedPlace);

// @route   GET /api/users/visited-status/:placeId
// @desc    Get visited place status
// @access  Private
router.get('/visited-status/:placeId', protect, [
  param('placeId')
    .isMongoId()
    .withMessage('Invalid place ID')
], handleValidationErrors, getVisitedPlaceStatus);

// @route   POST /api/users/bookmark/place/:placeId
// @desc    Toggle place bookmark
// @access  Private
router.post('/bookmark/place/:placeId', protect, [
  param('placeId')
    .isMongoId()
    .withMessage('Invalid place ID')
], togglePlaceBookmark);

// @route   POST /api/users/like/place/:placeId
// @desc    Toggle place like
// @access  Private
router.post('/like/place/:placeId', protect, [
  param('placeId')
    .isMongoId()
    .withMessage('Invalid place ID')
], togglePlaceLike);

// @route   GET /api/users/bookmarks
// @desc    Get user bookmarks
// @access  Private
router.get('/bookmarks', protect, [
  query('type')
    .optional()
    .isIn(['places', 'cities', 'likes'])
    .withMessage('Invalid bookmark type'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
], getUserBookmarks);

// @route   POST /api/users/bookmark/city/:cityId
// @desc    Toggle city bookmark
// @access  Private
router.post('/bookmark/city/:cityId', protect, [
  param('cityId')
    .isMongoId()
    .withMessage('Invalid city ID')
], toggleCityBookmark);

// @route   GET /api/users/place-status/:placeId
// @desc    Get place bookmark/like status
// @access  Private
router.get('/place-status/:placeId', protect, [
  param('placeId')
    .isMongoId()
    .withMessage('Invalid place ID')
], getPlaceStatus);

// @route   GET /api/users/city-status/:cityId
// @desc    Get city bookmark status
// @access  Private
router.get('/city-status/:cityId', protect, [
  param('cityId')
    .isMongoId()
    .withMessage('Invalid city ID')
], getCityStatus);

// @route   GET /api/users/:userId
// @desc    Get user profile
// @access  Public (if profile is public) / Private (own profile)
router.get('/:userId', [
  param('userId')
    .isMongoId()
    .withMessage('Invalid user ID')
], getUserProfile);

// @route   GET /api/users/:userId/bookmarks
// @desc    Get user bookmarks
// @access  Private (own bookmarks only)
router.get('/:userId/bookmarks', protect, [
  param('userId')
    .isMongoId()
    .withMessage('Invalid user ID'),
  query('type')
    .optional()
    .isIn(['cities', 'places', 'all'])
    .withMessage('Type must be cities, places, or all'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
], getUserBookmarks);

// @route   GET /api/users/:userId/contributions
// @desc    Get user contributions
// @access  Public (if profile is public) / Private (own contributions)
router.get('/:userId/contributions', [
  param('userId')
    .isMongoId()
    .withMessage('Invalid user ID'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
], getUserContributions);

// @route   PUT /api/users/:userId/preferences
// @desc    Update user preferences
// @access  Private (own preferences only)
router.put('/:userId/preferences', protect, [
  param('userId')
    .isMongoId()
    .withMessage('Invalid user ID')
], updateUserPreferences);

export default router;
