import mongoose from 'mongoose';

// Import and register all models explicitly
import UserModel from './User';
import AdminModel from './Admin';
import CityModel from './City';
import PlaceModel from './Place';
import ReviewModel from './Review';
import { Itinerary as ItineraryModel } from './Itinerary';

// Ensure models are registered
const User = mongoose.models.User || UserModel;
const Admin = mongoose.models.Admin || AdminModel;
const City = mongoose.models.City || CityModel;
const Place = mongoose.models.Place || PlaceModel;
const Review = mongoose.models.Review || ReviewModel;
const Itinerary = mongoose.models.Itinerary || ItineraryModel;

// Export models
export { User, Admin, City, Place, Review, Itinerary };
