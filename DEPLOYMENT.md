# HeritEdge Deployment Guide

This guide covers deploying HeritEdge platform using Docker and Kubernetes.

## 🏗️ Architecture

The HeritEdge platform consists of:
- **Backend API** (Node.js/Express/TypeScript) - Port 5001
- **Frontend** (Next.js) - Port 3000  
- **Admin Panel** (Next.js) - Port 3001
- **MongoDB Database** - Port 27017

## 📋 Prerequisites

### For Docker Deployment:
- Docker 20.10+
- Docker Compose 2.0+

### For Kubernetes Deployment:
- Docker 20.10+
- Kubernetes cluster (local or remote)
- kubectl configured
- At least 4GB RAM and 10GB storage

## 🐳 Docker Compose Deployment (Recommended for Development)

### Quick Start

1. **Clone and navigate to project:**
   ```bash
   cd /path/to/heritedge
   ```

2. **Build and start all services:**
   ```bash
   chmod +x scripts/docker-compose-build.sh
   ./scripts/docker-compose-build.sh
   ```

3. **Access the applications:**
   - Frontend: http://localhost:3000
   - Admin Panel: http://localhost:3001
   - Backend API: http://localhost:5001

### Manual Docker Compose Commands

```bash
# Build all images
docker-compose build

# Start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Clean up everything
docker-compose down -v --rmi all
```

## ☸️ Kubernetes Deployment

### Quick Start

1. **Build Docker images:**
   ```bash
   chmod +x scripts/build-images.sh
   ./scripts/build-images.sh
   ```

2. **Deploy to Kubernetes:**
   ```bash
   chmod +x scripts/deploy-k8s.sh
   ./scripts/deploy-k8s.sh
   ```

3. **Access the applications:**
   - Frontend: http://localhost:30000
   - Admin Panel: http://localhost:30002
   - Backend API: http://localhost:30001

### Manual Kubernetes Commands

```bash
# Create namespace
kubectl apply -f k8s/namespace.yaml

# Apply configurations
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml
kubectl apply -f k8s/persistent-volumes.yaml

# Deploy services
kubectl apply -f k8s/mongodb-deployment.yaml
kubectl apply -f k8s/backend-deployment.yaml
kubectl apply -f k8s/frontend-deployment.yaml
kubectl apply -f k8s/admin-deployment.yaml

# Check status
kubectl get pods -n heritedge
kubectl get services -n heritedge
```

### Cleanup Kubernetes Deployment

```bash
chmod +x scripts/cleanup-k8s.sh
./scripts/cleanup-k8s.sh
```

## 🔧 Configuration

### Environment Variables

#### Backend
- `NODE_ENV`: production
- `PORT`: 5001
- `MONGODB_URI`: MongoDB connection string
- `JWT_SECRET`: JWT signing secret
- `JWT_REFRESH_SECRET`: JWT refresh secret
- `FRONTEND_URL`: Frontend URL for CORS
- `ADMIN_URL`: Admin panel URL for CORS

#### Frontend
- `NEXT_PUBLIC_API_URL`: Backend API URL
- `NEXT_PUBLIC_FRONTEND_URL`: Frontend URL
- `NEXTAUTH_URL`: NextAuth URL
- `NEXTAUTH_SECRET`: NextAuth secret

#### Admin
- `NEXT_PUBLIC_API_URL`: Backend API URL
- `NEXT_PUBLIC_APP_NAME`: Application name
- `NEXT_PUBLIC_APP_VERSION`: Application version

### Default Credentials

**Admin Panel:**
- Email: `<EMAIL>`
- Password: `admin123456`

⚠️ **Change these credentials immediately after first login!**

## 📊 Monitoring

### Docker Compose
```bash
# View all service logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f admin

# Check service status
docker-compose ps
```

### Kubernetes
```bash
# View all pods
kubectl get pods -n heritedge

# View pod logs
kubectl logs -f deployment/heritedge-backend-deployment -n heritedge
kubectl logs -f deployment/heritedge-frontend-deployment -n heritedge
kubectl logs -f deployment/heritedge-admin-deployment -n heritedge

# Monitor pod status
kubectl get pods -n heritedge -w
```

## 🔒 Security Considerations

### Production Deployment

1. **Change Default Secrets:**
   - Update JWT secrets in `k8s/secrets.yaml`
   - Update MongoDB credentials
   - Update NextAuth secret

2. **Use TLS/SSL:**
   - Configure HTTPS for all services
   - Use proper certificates

3. **Network Security:**
   - Configure proper firewall rules
   - Use network policies in Kubernetes
   - Restrict database access

4. **Resource Limits:**
   - Set appropriate CPU/memory limits
   - Configure storage quotas

## 🚨 Troubleshooting

### Common Issues

1. **Images not found:**
   ```bash
   # Rebuild images
   ./scripts/build-images.sh
   ```

2. **Pods stuck in Pending:**
   ```bash
   # Check persistent volumes
   kubectl get pv,pvc -n heritedge
   ```

3. **Services not accessible:**
   ```bash
   # Check service endpoints
   kubectl get endpoints -n heritedge
   ```

4. **Database connection issues:**
   ```bash
   # Check MongoDB logs
   kubectl logs -f deployment/mongodb-deployment -n heritedge
   ```

### Health Checks

All services include health checks:
- Backend: `GET /health`
- Frontend: `GET /`
- Admin: `GET /`

## 📁 File Structure

```
├── backend/
│   ├── Dockerfile
│   └── src/
├── frontend/
│   ├── Dockerfile
│   └── src/
├── admin/
│   ├── Dockerfile
│   └── src/
├── k8s/
│   ├── namespace.yaml
│   ├── configmap.yaml
│   ├── secrets.yaml
│   ├── persistent-volumes.yaml
│   ├── mongodb-deployment.yaml
│   ├── backend-deployment.yaml
│   ├── frontend-deployment.yaml
│   └── admin-deployment.yaml
├── scripts/
│   ├── build-images.sh
│   ├── deploy-k8s.sh
│   ├── cleanup-k8s.sh
│   └── docker-compose-build.sh
├── docker-compose.yml
└── DEPLOYMENT.md
```

## 🆘 Support

For deployment issues:
1. Check the logs using the monitoring commands above
2. Verify all prerequisites are met
3. Ensure Docker/Kubernetes is properly configured
4. Check network connectivity between services
