#!/bin/bash

# HeritEdge Docker Compose Build Script
echo "🐳 Building HeritEdge with Docker Compose..."

# Set error handling
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Use docker compose if available, otherwise fall back to docker-compose
DOCKER_COMPOSE_CMD="docker compose"
if ! docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
fi

print_status "Using: $DOCKER_COMPOSE_CMD"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running ✓"

# Build all images
print_status "Building all Docker images..."
$DOCKER_COMPOSE_CMD build --no-cache

print_success "All images built successfully! 🎉"

# Start the services
print_status "Starting services..."
$DOCKER_COMPOSE_CMD up -d

# Wait for services to be healthy
print_status "Waiting for services to be ready..."
sleep 10

# Check service status
print_status "Checking service status..."
$DOCKER_COMPOSE_CMD ps

print_success "🌐 Services are running!"
echo ""
print_status "Access URLs:"
echo "Frontend: http://localhost:3000"
echo "Admin: http://localhost:3001"
echo "Backend API: http://localhost:5001"
echo ""

print_success "🔐 Default Admin Credentials:"
echo "Email: <EMAIL>"
echo "Password: admin123456"
echo ""

print_status "📊 To monitor the services:"
echo "$DOCKER_COMPOSE_CMD logs -f"
echo ""

print_status "🛑 To stop the services:"
echo "$DOCKER_COMPOSE_CMD down"
echo ""

print_status "🗑️  To clean up everything (including volumes):"
echo "$DOCKER_COMPOSE_CMD down -v --rmi all"
