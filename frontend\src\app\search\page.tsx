'use client';

import { useState, useEffect, useCallback, useMemo, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  MagnifyingGlassIcon,
  MapPinIcon,
  BuildingLibraryIcon,
  GlobeAltIcon,
  ClockIcon,
  HeartIcon,
  EyeIcon,
  CalendarDaysIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import { apiClient } from '@/lib/api';
import { Breadcrumb } from '@/components/ui/Breadcrumb';

interface SearchResult {
  type: 'city' | 'place' | 'itinerary';
  id: string;
  title: string;
  description?: string;
  image?: string;
  location?: string;
  country?: string;
  category?: string;
  rating?: number;
  likes?: number;
  views?: number;
  duration?: number;
  user?: {
    name: string;
  };
  createdAt?: string;
}

function SearchContent() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'cities' | 'places' | 'itineraries'>('all');

  // Static list of available cities to avoid API rate limits
  const availableCities = useMemo(() => [
    { name: 'Paris', country: 'France', slug: 'paris', description: 'City of Light and Romance', image: '/assets/cities/paris.jpg' },
    { name: 'London', country: 'United Kingdom', slug: 'london', description: 'Historic Capital of England', image: '/assets/cities/london.jpg' },
    { name: 'Rome', country: 'Italy', slug: 'rome', description: 'The Eternal City', image: '/assets/cities/rome.jpg' },
    { name: 'Tokyo', country: 'Japan', slug: 'tokyo', description: 'Modern Metropolis', image: '/assets/cities/tokyo.jpg' },
    { name: 'New York', country: 'United States', slug: 'new-york', description: 'The Big Apple', image: '/assets/cities/new-york.jpg' },
    { name: 'Barcelona', country: 'Spain', slug: 'barcelona', description: 'Gaudí\'s Architectural Marvel', image: '/assets/cities/barcelona.jpg' },
    { name: 'Amsterdam', country: 'Netherlands', slug: 'amsterdam', description: 'Venice of the North', image: '/assets/cities/amsterdam.jpg' },
    { name: 'Prague', country: 'Czech Republic', slug: 'prague', description: 'City of a Hundred Spires', image: '/assets/cities/prague.jpg' },
    { name: 'Istanbul', country: 'Turkey', slug: 'istanbul', description: 'Bridge Between Continents', image: '/assets/cities/istanbul.jpg' },
    { name: 'Cairo', country: 'Egypt', slug: 'cairo', description: 'Gateway to Ancient Egypt', image: '/assets/cities/cairo.jpg' },
    { name: 'Athens', country: 'Greece', slug: 'athens', description: 'Cradle of Democracy', image: '/assets/cities/athens.jpg' },
    { name: 'Florence', country: 'Italy', slug: 'florence', description: 'Renaissance Jewel', image: '/assets/cities/florence.jpg' }
  ], []);

  const performSearch = useCallback(async (searchQuery: string) => {
    try {
      setLoading(true);
      setError(null);

      // Filter available cities based on search query
      const filteredCities = availableCities.filter(city =>
        city.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        city.country.toLowerCase().includes(searchQuery.toLowerCase()) ||
        city.description.toLowerCase().includes(searchQuery.toLowerCase())
      );

      // Convert to search result format
      const allResults: SearchResult[] = filteredCities.map(city => ({
        type: 'city' as const,
        id: city.slug,
        title: city.name,
        description: city.description,
        image: city.image,
        location: `${city.name}, ${city.country}`,
        country: city.country
      }));

      setResults(allResults);
    } catch (err: any) {
      console.error('Search error:', err);
      setError('Search functionality is temporarily limited. Please browse our available cities below.');

      // Show all cities as fallback
      const allCities: SearchResult[] = availableCities.map(city => ({
        type: 'city' as const,
        id: city.slug,
        title: city.name,
        description: city.description,
        image: city.image,
        location: `${city.name}, ${city.country}`,
        country: city.country
      }));

      setResults(allCities);
    } finally {
      setLoading(false);
    }
  }, [availableCities]);

  useEffect(() => {
    if (query) {
      performSearch(query);
    }
  }, [query, performSearch]);

  const filteredResults = results.filter(result => {
    if (activeTab === 'all') return true;
    if (activeTab === 'cities') return result.type === 'city';
    if (activeTab === 'places') return result.type === 'place';
    if (activeTab === 'itineraries') return result.type === 'itinerary';
    return true;
  });

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'city':
        return <GlobeAltIcon className="h-5 w-5" />;
      case 'place':
        return <BuildingLibraryIcon className="h-5 w-5" />;
      case 'itinerary':
        return <CalendarDaysIcon className="h-5 w-5" />;
      default:
        return <MagnifyingGlassIcon className="h-5 w-5" />;
    }
  };

  const getResultLink = (result: SearchResult) => {
    switch (result.type) {
      case 'city':
        return `/cities/${result.id}`;
      case 'place':
        return `/places/${result.id}`;
      case 'itinerary':
        return `/itineraries/${result.id}`;
      default:
        return '#';
    }
  };

  const getTabCounts = () => {
    return {
      all: results.length,
      cities: results.filter(r => r.type === 'city').length,
      places: results.filter(r => r.type === 'place').length,
      itineraries: results.filter(r => r.type === 'itinerary').length
    };
  };

  const tabCounts = getTabCounts();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <MagnifyingGlassIcon className="h-16 w-16 text-white mx-auto mb-4" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Search Results</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              {query ? `Results for "${query}"` : 'Discover amazing destinations and experiences'}
            </p>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <Breadcrumb 
            items={[
              { label: 'Search', href: '/search' },
              { label: query || 'Results', isActive: true }
            ]} 
          />
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Search Limited to Available Cities
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  Search is currently limited to our curated list of {availableCities.length} heritage cities.
                  You can search by city name, country, or description to find your destination.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Search Stats and Filters */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div className="text-gray-600">
            {loading ? (
              <span>Searching...</span>
            ) : (
              <span>
                Found {filteredResults.length} result{filteredResults.length !== 1 ? 's' : ''} 
                {query && ` for "${query}"`}
              </span>
            )}
          </div>

          {/* Filter Tabs */}
          <div className="flex space-x-1 bg-white rounded-lg p-1 border border-gray-200">
            {[
              { key: 'all', label: 'All Cities', count: tabCounts.all },
              { key: 'cities', label: 'Available Cities', count: tabCounts.cities }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.key
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                {tab.label} ({tab.count})
              </button>
            ))}
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm p-6 animate-pulse">
                <div className="h-48 bg-gray-200 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-16">
            <MagnifyingGlassIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Search Error</h3>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => query && performSearch(query)}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Results Grid */}
        {!loading && !error && filteredResults.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredResults.map((result) => (
              <Link
                key={`${result.type}-${result.id}`}
                href={getResultLink(result)}
                className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden group"
              >
                <div className="relative h-48 bg-gray-200">
                  {result.image ? (
                    <Image
                      src={result.image}
                      alt={result.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-200"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full bg-gradient-to-br from-blue-100 to-purple-100">
                      {getResultIcon(result.type)}
                    </div>
                  )}
                  <div className="absolute top-3 left-3">
                    <span className="px-2 py-1 text-xs font-medium bg-white/90 text-gray-700 rounded-full capitalize">
                      {result.type}
                    </span>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                    {result.title}
                  </h3>
                  
                  {result.location && (
                    <div className="flex items-center text-gray-600 mb-2">
                      <MapPinIcon className="h-4 w-4 mr-1" />
                      <span className="text-sm">{result.location}</span>
                    </div>
                  )}

                  {result.description && (
                    <p className="text-gray-600 text-sm line-clamp-2 mb-4">
                      {result.description}
                    </p>
                  )}

                  {/* Type-specific metadata */}
                  {result.type === 'place' && result.category && (
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span className="capitalize">{result.category}</span>
                      {result.rating && (
                        <span className="flex items-center">
                          ⭐ {result.rating.toFixed(1)}
                        </span>
                      )}
                    </div>
                  )}

                  {result.type === 'itinerary' && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center">
                          <ClockIcon className="h-4 w-4 mr-1" />
                          <span>{result.duration} days</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center">
                            <HeartIconSolid className="h-4 w-4 mr-1 text-red-500" />
                            <span>{result.likes}</span>
                          </div>
                          <div className="flex items-center">
                            <EyeIcon className="h-4 w-4 mr-1" />
                            <span>{result.views}</span>
                          </div>
                        </div>
                      </div>
                      {result.user && (
                        <div className="flex items-center text-xs text-gray-500">
                          <UserIcon className="h-3 w-3 mr-1" />
                          <span>by {result.user.name}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </Link>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && filteredResults.length === 0 && query && (
          <div className="text-center py-16">
            <MagnifyingGlassIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No Results Found</h3>
            <p className="text-gray-600 mb-6">
              We couldn&apos;t find any cities matching &quot;{query}&quot;. Try searching for one of our available destinations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/cities"
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Browse Cities
              </Link>
              <Link
                href="/places"
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Explore Places
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    }>
      <SearchContent />
    </Suspense>
  );
}
