const axios = require('axios');

async function testReviewsEndpoint() {
  try {
    console.log('🧪 Testing admin login...');
    
    // Test admin login
    const loginResponse = await axios.post('http://localhost:5001/api/admin/auth/login', {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Admin login successful');
      const token = loginResponse.data.data.token;
      
      // Test reviews endpoint (the failing endpoint)
      console.log('🧪 Testing admin reviews endpoint...');
      const reviewsResponse = await axios.get('http://localhost:5001/api/admin/reviews?page=1&limit=10&sortBy=createdAt&sortOrder=desc', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (reviewsResponse.data.success) {
        console.log('✅ Admin reviews endpoint successful:', reviewsResponse.data.data.items.length, 'reviews found');
        if (reviewsResponse.data.data.items.length > 0) {
          console.log('📊 Sample review:', JSON.stringify(reviewsResponse.data.data.items[0], null, 2));
        }
      } else {
        console.error('❌ Admin reviews endpoint failed:', reviewsResponse.data);
      }
      
    } else {
      console.error('❌ Admin login failed:', loginResponse.data);
    }
    
  } catch (error) {
    if (error.response) {
      console.error('❌ Test failed:', error.response.status, error.response.data);
    } else {
      console.error('❌ Test failed:', error.message);
    }
  }
}

testReviewsEndpoint();
