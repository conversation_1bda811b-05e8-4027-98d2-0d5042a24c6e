const mongoose = require('mongoose');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/heritedge';

async function importLondonPlaces() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully');

    // Get collections
    const citiesCollection = mongoose.connection.db.collection('cities');
    const placesCollection = mongoose.connection.db.collection('places');

    // Find London city
    const londonCity = await citiesCollection.findOne({ slug: 'london' });
    if (!londonCity) {
      console.log('❌ London city not found. Please make sure London city exists in the database.');
      console.log('💡 You may need to add London to the cities collection first.');
      return;
    }

    console.log(`✅ Found London city: ${londonCity.name} (ID: ${londonCity._id})`);

    // Load London places data
    const placesData = require('./london-places.json');
    
    // Clear existing places for London
    await placesCollection.deleteMany({ city: londonCity._id });
    console.log('🗑️ Cleared existing places for London');

    // Update places data with correct city ObjectId
    const updatedPlaces = placesData.map(place => ({
      ...place,
      city: londonCity._id,
      createdAt: new Date(),
      updatedAt: new Date()
    }));

    // Insert places
    const result = await placesCollection.insertMany(updatedPlaces);
    console.log(`✅ Successfully imported ${result.insertedCount} places for London`);

    // Verify and display results
    const totalPlaces = await placesCollection.countDocuments({ city: londonCity._id });
    console.log(`📊 Total places for London: ${totalPlaces}`);

    // Group by category
    const placesByCategory = await placesCollection.aggregate([
      { $match: { city: londonCity._id } },
      { $group: { _id: '$category', count: { $sum: 1 }, places: { $push: '$name' } } },
      { $sort: { _id: 1 } }
    ]).toArray();

    console.log('\n📍 London places by category:');
    placesByCategory.forEach(category => {
      console.log(`\n🏷️  ${category._id.toUpperCase()} (${category.count} places):`);
      category.places.forEach(placeName => {
        console.log(`   - ${placeName}`);
      });
    });

    console.log('\n🎉 London places import completed successfully!');
    console.log('🌐 You can now view London places at: http://localhost:3001/cities/london');
    console.log('📱 Click the "Places" tab to see the categorized places in a 2x2 grid layout');

  } catch (error) {
    console.error('❌ Failed to import London places:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  importLondonPlaces();
}

module.exports = { importLondonPlaces };
