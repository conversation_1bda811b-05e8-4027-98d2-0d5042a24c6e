import express from 'express';
import {
  submitVideo,
  getPlaceVideos,
  getUserVideos,
  getPendingVideos,
  reviewVideo,
  deleteVideo,
  incrementViewCount
} from '../controllers/videoController';
import { protect, authorize } from '../middleware/auth';
import { body, param } from 'express-validator';
import { validate } from '../middleware/validation';

const router = express.Router();

// Validation middleware
const submitVideoValidation = [
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description cannot exceed 1000 characters'),
  body('youtubeUrl')
    .isURL()
    .withMessage('Must be a valid URL')
    .matches(/^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/)
    .withMessage('Must be a valid YouTube URL'),
  body('placeId')
    .isMongoId()
    .withMessage('Invalid place ID'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('tags.*')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Each tag must be between 1 and 50 characters')
];

const reviewVideoValidation = [
  param('videoId')
    .isMongoId()
    .withMessage('Invalid video ID'),
  body('status')
    .isIn(['approved', 'rejected'])
    .withMessage('Status must be either "approved" or "rejected"'),
  body('reviewNotes')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Review notes cannot exceed 500 characters')
];

const videoIdValidation = [
  param('videoId')
    .isMongoId()
    .withMessage('Invalid video ID')
];

const placeIdValidation = [
  param('placeId')
    .isMongoId()
    .withMessage('Invalid place ID')
];

// Public routes
// Get approved videos for a specific place
router.get('/place/:placeId', placeIdValidation, validate, getPlaceVideos);

// Increment view count for a video
router.post('/:videoId/view', videoIdValidation, validate, incrementViewCount);

// Protected routes (require authentication)
// Submit a new video for approval
router.post('/submit', protect, submitVideoValidation, validate, submitVideo);

// Get user's submitted videos
router.get('/my-videos', protect, getUserVideos);

// Delete a video (user can delete their own, admin can delete any)
router.delete('/:videoId', protect, videoIdValidation, validate, deleteVideo);

// Admin routes
// Get pending videos for review
router.get('/admin/pending', protect, authorize('admin'), getPendingVideos);

// Approve or reject a video
router.patch('/admin/:videoId/review', protect, authorize('admin'), reviewVideoValidation, validate, reviewVideo);

export default router;
