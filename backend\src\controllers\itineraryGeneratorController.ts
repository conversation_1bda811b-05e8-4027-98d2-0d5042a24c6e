import { Response } from 'express';
import { validationResult } from 'express-validator';
import { Itinerary } from '../models/Itinerary';
import City from '../models/City';
import Place from '../models/Place';
import { AuthRequest } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

interface GenerationRequest {
  cityId: string;
  duration: number;
  startDate: string;
  budget: 'budget' | 'mid-range' | 'luxury';
  travelStyle: 'solo' | 'couple' | 'family' | 'group' | 'business';
  interests: string[];
  accommodationPreference?: string;
  transportPreference?: string;
  specialRequests?: string;
}

// @desc    Generate detailed itinerary with AI recommendations
// @route   POST /api/itineraries/generate
// @access  Private
export const generateItinerary = asyncHandler(async (req: AuthRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const {
    cityId,
    duration,
    startDate,
    budget,
    travelStyle,
    interests,
    accommodationPreference,
    transportPreference,
    specialRequests
  }: GenerationRequest = req.body;

  // Verify city exists
  const city = await City.findById(cityId).populate('places');
  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  // Get places in the city that match interests
  const places = await Place.find({
    city: cityId,
    $or: [
      { category: { $in: interests } },
      { tags: { $in: interests } }
    ]
  }).sort({ averageRating: -1 });

  if (places.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'No places found matching your interests in this city'
    });
  }

  // Generate day-by-day itinerary
  const generatedDays = generateDayPlans({
    places,
    duration,
    startDate,
    budget,
    travelStyle,
    interests,
    specialRequests
  });

  // Generate recommendations
  const recommendations = generateRecommendations({
    city: city.name,
    budget,
    travelStyle,
    accommodationPreference,
    transportPreference,
    specialRequests
  });

  res.json({
    success: true,
    data: {
      days: generatedDays,
      recommendations,
      totalPlaces: places.length,
      generationPrompt: `Generated ${duration}-day ${budget} ${travelStyle} itinerary for ${city.name} focusing on ${interests.join(', ')}`
    }
  });
});

// Helper function to generate day plans
function generateDayPlans(params: {
  places: any[];
  duration: number;
  startDate: string;
  budget: string;
  travelStyle: string;
  interests: string[];
  specialRequests?: string;
}) {
  const { places, duration, startDate, budget, travelStyle, interests } = params;
  const days = [];
  const start = new Date(startDate);
  
  // Categorize places by type and priority
  const categorizedPlaces = categorizePlaces(places, interests);
  
  // Distribute places across days
  const placesPerDay = Math.ceil(places.length / duration);
  let placeIndex = 0;

  for (let i = 0; i < duration; i++) {
    const dayDate = new Date(start);
    dayDate.setDate(start.getDate() + i);
    
    const dayPlaces = [];
    const dayTheme = getDayTheme(i, duration, interests);
    
    // Morning activity (usually major attraction)
    if (placeIndex < places.length) {
      const morningPlace = selectPlaceForTimeSlot('morning', categorizedPlaces, dayTheme, budget);
      if (morningPlace) {
        dayPlaces.push({
          place: morningPlace._id,
          timeSlot: 'morning',
          duration: getMorningDuration(morningPlace.category, travelStyle),
          notes: generatePlaceNotes(morningPlace, 'morning', budget),
          order: 1
        });
        placeIndex++;
      }
    }

    // Afternoon activity
    if (placeIndex < places.length) {
      const afternoonPlace = selectPlaceForTimeSlot('afternoon', categorizedPlaces, dayTheme, budget);
      if (afternoonPlace) {
        dayPlaces.push({
          place: afternoonPlace._id,
          timeSlot: 'afternoon',
          duration: getAfternoonDuration(afternoonPlace.category, travelStyle),
          notes: generatePlaceNotes(afternoonPlace, 'afternoon', budget),
          order: 2
        });
        placeIndex++;
      }
    }

    // Evening activity (usually dining or entertainment)
    if (placeIndex < places.length) {
      const eveningPlace = selectPlaceForTimeSlot('evening', categorizedPlaces, dayTheme, budget);
      if (eveningPlace) {
        dayPlaces.push({
          place: eveningPlace._id,
          timeSlot: 'evening',
          duration: getEveningDuration(eveningPlace.category, travelStyle),
          notes: generatePlaceNotes(eveningPlace, 'evening', budget),
          order: 3
        });
        placeIndex++;
      }
    }

    days.push({
      dayNumber: i + 1,
      date: dayDate,
      title: `Day ${i + 1}: ${dayTheme}`,
      places: dayPlaces,
      notes: generateDayNotes(i + 1, dayTheme, travelStyle, budget)
    });
  }

  return days;
}

// Helper function to categorize places
function categorizePlaces(places: any[], interests: string[]) {
  return {
    attractions: places.filter(p => ['attraction', 'museum', 'historical', 'religious'].includes(p.category)),
    restaurants: places.filter(p => p.category === 'restaurant'),
    entertainment: places.filter(p => ['entertainment', 'nightlife'].includes(p.category)),
    shopping: places.filter(p => p.category === 'shopping'),
    nature: places.filter(p => ['nature', 'park'].includes(p.category)),
    cultural: places.filter(p => ['museum', 'art', 'cultural'].includes(p.category))
  };
}

// Helper function to get day theme
function getDayTheme(dayIndex: number, totalDays: number, interests: string[]): string {
  const themes = [
    'Historic Exploration',
    'Cultural Immersion',
    'Local Experiences',
    'Art & Architecture',
    'Food & Markets',
    'Nature & Relaxation',
    'Shopping & Leisure'
  ];

  // Prioritize themes based on interests
  const prioritizedThemes = themes.filter(theme => {
    const themeKeywords = theme.toLowerCase().split(' ');
    return interests.some(interest => 
      themeKeywords.some(keyword => interest.toLowerCase().includes(keyword))
    );
  });

  if (prioritizedThemes.length > 0) {
    return prioritizedThemes[dayIndex % prioritizedThemes.length];
  }

  return themes[dayIndex % themes.length];
}

// Helper function to select place for time slot
function selectPlaceForTimeSlot(timeSlot: string, categorizedPlaces: any, dayTheme: string, budget: string) {
  let candidates = [];

  switch (timeSlot) {
    case 'morning':
      candidates = [...categorizedPlaces.attractions, ...categorizedPlaces.cultural, ...categorizedPlaces.nature];
      break;
    case 'afternoon':
      candidates = [...categorizedPlaces.attractions, ...categorizedPlaces.shopping, ...categorizedPlaces.cultural];
      break;
    case 'evening':
      candidates = [...categorizedPlaces.restaurants, ...categorizedPlaces.entertainment];
      break;
  }

  // Filter by budget if needed
  if (budget === 'budget') {
    candidates = candidates.filter(p => !p.pricing || p.pricing.level <= 2);
  } else if (budget === 'luxury') {
    candidates = candidates.filter(p => !p.pricing || p.pricing.level >= 3);
  }

  // Return highest rated available place
  return candidates.sort((a, b) => (b.averageRating || 0) - (a.averageRating || 0))[0];
}

// Helper functions for duration
function getMorningDuration(category: string, travelStyle: string): number {
  const baseDuration = category === 'museum' ? 3 : 2;
  return travelStyle === 'family' ? baseDuration + 0.5 : baseDuration;
}

function getAfternoonDuration(category: string, travelStyle: string): number {
  const baseDuration = category === 'shopping' ? 2.5 : 2;
  return travelStyle === 'family' ? baseDuration + 0.5 : baseDuration;
}

function getEveningDuration(category: string, travelStyle: string): number {
  const baseDuration = category === 'restaurant' ? 1.5 : 2;
  return travelStyle === 'couple' ? baseDuration + 0.5 : baseDuration;
}

// Helper function to generate place notes
function generatePlaceNotes(place: any, timeSlot: string, budget: string): string {
  const notes = [];

  if (timeSlot === 'morning') {
    notes.push('Best visited early to avoid crowds');
  } else if (timeSlot === 'evening' && place.category === 'restaurant') {
    notes.push('Consider making a reservation');
  }

  if (budget === 'budget') {
    notes.push('Look for student discounts or free entry times');
  } else if (budget === 'luxury') {
    notes.push('Consider guided tours or premium experiences');
  }

  if (place.operatingHours) {
    notes.push(`Operating hours: ${place.operatingHours}`);
  }

  return notes.join('. ');
}

// Helper function to generate day notes
function generateDayNotes(dayNumber: number, theme: string, travelStyle: string, budget: string): string {
  const notes = [];

  notes.push(`Focus on ${theme.toLowerCase()}`);

  if (travelStyle === 'family') {
    notes.push('Take breaks between activities for children');
  } else if (travelStyle === 'couple') {
    notes.push('Perfect for romantic exploration');
  }

  if (budget === 'budget') {
    notes.push('Use public transport and look for free activities');
  } else if (budget === 'luxury') {
    notes.push('Consider private transport and premium experiences');
  }

  return notes.join('. ');
}

// Helper function to generate recommendations
function generateRecommendations(params: {
  city: string;
  budget: string;
  travelStyle: string;
  accommodationPreference?: string;
  transportPreference?: string;
  specialRequests?: string;
}) {
  const { city, budget, travelStyle, accommodationPreference, transportPreference } = params;

  return {
    generalTips: [
      `Best time to visit ${city} is early morning or late afternoon`,
      'Download offline maps before exploring',
      'Keep copies of important documents',
      'Learn basic local phrases'
    ],
    culturalEtiquette: [
      'Dress modestly when visiting religious sites',
      'Remove shoes when entering temples or homes',
      'Respect local customs and traditions',
      'Ask permission before photographing people'
    ],
    safetyTips: [
      'Stay aware of your surroundings',
      'Keep valuables secure',
      'Use official transportation services',
      'Have emergency contacts readily available'
    ],
    localTransportInfo: getTransportInfo(transportPreference, budget),
    recommendedAccommodations: getAccommodationRecommendations(accommodationPreference, budget, travelStyle)
  };
}

function getTransportInfo(preference?: string, budget?: string): string {
  if (preference === 'public') {
    return 'Use metro/bus passes for cost-effective travel. Download local transport apps.';
  } else if (preference === 'taxi') {
    return 'Use official taxi services or ride-sharing apps. Negotiate fares beforehand if no meter.';
  } else if (budget === 'budget') {
    return 'Public transport is most economical. Walking is great for short distances.';
  } else if (budget === 'luxury') {
    return 'Private car services offer comfort and convenience. Consider hiring a local driver.';
  }
  return 'Mix of walking, public transport, and taxis based on distance and convenience.';
}

function getAccommodationRecommendations(preference?: string, budget?: string, travelStyle?: string) {
  const recommendations = [];

  if (budget === 'budget') {
    recommendations.push({
      name: 'Budget Hotels & Hostels',
      type: 'budget',
      priceRange: '$20-50/night',
      location: 'City center or near public transport',
      description: 'Clean, basic accommodations with essential amenities'
    });
  } else if (budget === 'luxury') {
    recommendations.push({
      name: 'Luxury Hotels & Resorts',
      type: 'luxury',
      priceRange: '$200-500/night',
      location: 'Prime locations with city views',
      description: 'Premium amenities, concierge services, and exceptional comfort'
    });
  } else {
    recommendations.push({
      name: 'Mid-range Hotels',
      type: 'mid-range',
      priceRange: '$80-150/night',
      location: 'Good locations with easy access to attractions',
      description: 'Comfortable rooms with modern amenities and good service'
    });
  }

  return recommendations;
}
