import { Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { async<PERSON><PERSON><PERSON> } from './errorHandler';
import { AdminAuthRequest } from '../controllers/adminAuthController';

// Import models from the index to ensure they're registered
import { Admin } from '../models';
import { IAdmin } from '../models/Admin';

// Protect admin routes
export const protectAdmin = asyncHandler(async (req: AdminAuthRequest, res: Response, next: NextFunction) => {
  let token;

  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Not authorized to access this route'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as { id: string; type: string };
    
    // Check if token is for admin
    if (decoded.type !== 'admin') {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to access this route'
      });
    }
    
    const admin = await Admin.findById(decoded.id);
    
    if (!admin) {
      return res.status(401).json({
        success: false,
        error: 'No admin found with this token'
      });
    }

    // Check if admin is active
    if (!admin.isActive) {
      return res.status(401).json({
        success: false,
        error: 'Admin account has been deactivated'
      });
    }

    // Check if admin account is locked
    const isLocked = admin.lockUntil && admin.lockUntil > new Date();
    if (isLocked) {
      return res.status(423).json({
        success: false,
        error: 'Admin account is temporarily locked'
      });
    }

    req.admin = admin;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: 'Not authorized to access this route'
    });
  }
});

// Authorize admin roles
export const authorizeAdmin = (...roles: string[]) => {
  return (req: AdminAuthRequest, res: Response, next: NextFunction) => {
    if (!req.admin) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to access this route'
      });
    }

    if (!roles.includes(req.admin.role)) {
      return res.status(403).json({
        success: false,
        error: `Admin role ${req.admin.role} is not authorized to access this route`
      });
    }

    next();
  };
};

// Check if super admin
export const requireSuperAdmin = authorizeAdmin('super_admin');

// Check if admin or super admin
export const requireAdmin = authorizeAdmin('super_admin', 'admin');

// Check if moderator, admin, or super admin
export const requireModerator = authorizeAdmin('super_admin', 'admin', 'moderator');
