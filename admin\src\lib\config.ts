// Environment detection
const isKubernetes = process.env.KUBERNETES_SERVICE_HOST !== undefined;
const isProduction = process.env.NODE_ENV === 'production';

// Default URLs based on environment
const getDefaultApiUrl = () => {
  if (isKubernetes) {
    // In Kubernetes, use service name
    return 'http://backend-service:5001/api';
  }
  // Local development
  return 'http://localhost:5001/api';
};

const getDefaultServerUrl = () => {
  if (isKubernetes) {
    return 'http://backend-service:5001';
  }
  return 'http://localhost:5001';
};

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || getDefaultApiUrl(),
  SERVER_URL: process.env.NEXT_PUBLIC_SERVER_URL || getDefaultServerUrl(),
  UPLOADS_URL: process.env.NEXT_PUBLIC_UPLOADS_URL || `${getDefaultServerUrl()}/uploads`,
  IS_KUBERNETES: isKubernetes,
  IS_PRODUCTION: isProduction,
};

// Helper function to get full media URL
export const getMediaUrl = (path: string): string => {
  if (path.startsWith('http')) {
    return path; // Already a full URL
  }
  
  if (path.startsWith('/uploads')) {
    return `${API_CONFIG.SERVER_URL}${path}`;
  }
  
  return `${API_CONFIG.UPLOADS_URL}/${path}`;
};

// Helper function to get API endpoint URL
export const getApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
};

export default API_CONFIG;
