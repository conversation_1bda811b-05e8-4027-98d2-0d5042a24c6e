// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001/api',
  SERVER_URL: process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:5001',
  UPLOADS_URL: process.env.NEXT_PUBLIC_UPLOADS_URL || 'http://localhost:5001/uploads',
};

// Helper function to get full media URL
export const getMediaUrl = (path: string): string => {
  if (path.startsWith('http')) {
    return path; // Already a full URL
  }
  
  if (path.startsWith('/uploads')) {
    return `${API_CONFIG.SERVER_URL}${path}`;
  }
  
  return `${API_CONFIG.UPLOADS_URL}/${path}`;
};

// Helper function to get API endpoint URL
export const getApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
};

export default API_CONFIG;
