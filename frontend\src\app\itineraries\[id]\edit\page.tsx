'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  CalendarDaysIcon,
  PlusIcon,
  TrashIcon,
  ClockIcon,
  MapPinIcon,
  MagnifyingGlassIcon,
  CheckIcon,
  XMarkIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import { apiClient } from '@/lib/api';
import { Breadcrumb } from '@/components/ui/Breadcrumb';
import toast from 'react-hot-toast';

interface Place {
  _id: string;
  name: string;
  slug: string;
  category: string;
  images?: { url: string; alt: string }[];
  averageRating?: number;
  description?: string;
}

interface PlaceActivity {
  place: Place;
  timeSlot: 'morning' | 'afternoon' | 'evening' | 'night';
  duration?: number;
  notes?: string;
  order: number;
}

interface DayPlan {
  dayNumber: number;
  date: string;
  title?: string;
  places: PlaceActivity[];
  notes?: string;
}

interface Itinerary {
  _id: string;
  title: string;
  description?: string;
  city: {
    _id: string;
    name: string;
    slug: string;
    country: string;
  };
  duration: number;
  startDate: string;
  endDate: string;
  budget: string;
  travelStyle: string;
  interests: string[];
  days: DayPlan[];
  accommodationPreference?: string;
  transportPreference?: string;
  specialRequests?: string;
  status: string;
  isPublic: boolean;
}

export default function EditItineraryPage() {
  const { id } = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  
  const [itinerary, setItinerary] = useState<Itinerary | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeDay, setActiveDay] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Place[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [showAddPlace, setShowAddPlace] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, router]);

  // Fetch itinerary
  useEffect(() => {
    const fetchItinerary = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await apiClient.getItinerary(id as string);
        
        if (response.success && response.data) {
          const itineraryData = response.data.itinerary;
          
          // Check if user is owner
          if (itineraryData.user._id !== user?.id) {
            setError('You can only edit your own itineraries');
            return;
          }
          
          setItinerary(itineraryData);
        } else {
          setError('Itinerary not found');
        }
      } catch (err: any) {
        console.error('Error fetching itinerary:', err);
        setError(err.message || 'Failed to load itinerary');
      } finally {
        setLoading(false);
      }
    };

    if (id && user) {
      fetchItinerary();
    }
  }, [id, user]);

  // Search places
  useEffect(() => {
    const searchPlaces = async () => {
      if (!searchQuery.trim() || !itinerary) return;

      try {
        setSearchLoading(true);
        const response = await apiClient.getPlaces({
          search: searchQuery,
          city: itinerary.city._id,
          limit: 10
        });
        
        if (response.success && response.data) {
          setSearchResults(response.data.places || []);
        }
      } catch (error) {
        console.error('Search error:', error);
      } finally {
        setSearchLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchPlaces, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery, itinerary]);

  const handleSave = async () => {
    if (!itinerary) return;

    try {
      setSaving(true);
      
      const response = await apiClient.updateItinerary(itinerary._id, {
        days: itinerary.days
      });
      
      if (response.success) {
        toast.success('Itinerary updated successfully!');
        router.push(`/itineraries/${itinerary._id}`);
      }
    } catch (error: any) {
      console.error('Save error:', error);
      toast.error(error.message || 'Failed to save itinerary');
    } finally {
      setSaving(false);
    }
  };

  const addPlaceToDay = (place: Place, timeSlot: string) => {
    if (!itinerary) return;

    const dayIndex = activeDay - 1;
    const newActivity: PlaceActivity = {
      place,
      timeSlot: timeSlot as any,
      duration: 2, // Default 2 hours
      notes: '',
      order: itinerary.days[dayIndex].places.length + 1
    };

    const updatedDays = [...itinerary.days];
    updatedDays[dayIndex].places.push(newActivity);

    setItinerary({ ...itinerary, days: updatedDays });
    setShowAddPlace(false);
    setSearchQuery('');
    setSearchResults([]);
    toast.success(`Added ${place.name} to Day ${activeDay}`);
  };

  const removePlaceFromDay = (dayIndex: number, placeIndex: number) => {
    if (!itinerary) return;

    const updatedDays = [...itinerary.days];
    updatedDays[dayIndex].places.splice(placeIndex, 1);
    
    // Reorder remaining places
    updatedDays[dayIndex].places.forEach((place, index) => {
      place.order = index + 1;
    });

    setItinerary({ ...itinerary, days: updatedDays });
    toast.success('Place removed from itinerary');
  };

  const updatePlaceActivity = (dayIndex: number, placeIndex: number, updates: Partial<PlaceActivity>) => {
    if (!itinerary) return;

    const updatedDays = [...itinerary.days];
    updatedDays[dayIndex].places[placeIndex] = {
      ...updatedDays[dayIndex].places[placeIndex],
      ...updates
    };

    setItinerary({ ...itinerary, days: updatedDays });
  };

  const movePlaceOrder = (dayIndex: number, placeIndex: number, direction: 'up' | 'down') => {
    if (!itinerary) return;

    const updatedDays = [...itinerary.days];
    const places = updatedDays[dayIndex].places;
    
    if (direction === 'up' && placeIndex > 0) {
      [places[placeIndex], places[placeIndex - 1]] = [places[placeIndex - 1], places[placeIndex]];
    } else if (direction === 'down' && placeIndex < places.length - 1) {
      [places[placeIndex], places[placeIndex + 1]] = [places[placeIndex + 1], places[placeIndex]];
    }

    // Update order numbers
    places.forEach((place, index) => {
      place.order = index + 1;
    });

    setItinerary({ ...itinerary, days: updatedDays });
  };

  const updateDayNotes = (dayIndex: number, notes: string) => {
    if (!itinerary) return;

    const updatedDays = [...itinerary.days];
    updatedDays[dayIndex].notes = notes;
    setItinerary({ ...itinerary, days: updatedDays });
  };

  const getTimeSlotIcon = (timeSlot: string) => {
    switch (timeSlot) {
      case 'morning': return '🌅';
      case 'afternoon': return '☀️';
      case 'evening': return '🌆';
      case 'night': return '🌙';
      default: return '⏰';
    }
  };

  const getFallbackImage = (category?: string) => {
    const fallbacks = {
      museum: 'https://images.unsplash.com/photo-1566139884669-4b9356b4c040?w=400&q=80',
      religious: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&q=80',
      restaurant: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&q=80',
      attraction: 'https://images.unsplash.com/photo-1511739001486-6bfe10ce785f?w=400&q=80',
      default: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400&q=80'
    };
    return fallbacks[category as keyof typeof fallbacks] || fallbacks.default;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              <div className="h-96 bg-gray-200 rounded-xl"></div>
              <div className="lg:col-span-3 h-96 bg-gray-200 rounded-xl"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !itinerary) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Error</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => router.back()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <Breadcrumb items={[
                { label: 'Itineraries', href: '/itineraries' },
                { label: itinerary.title, href: `/itineraries/${itinerary._id}` },
                { label: 'Edit', isActive: true }
              ]} />
              <h1 className="text-3xl font-bold text-gray-900 mt-2">Edit Itinerary</h1>
              <p className="text-gray-600">{itinerary.title} • {itinerary.city.name}, {itinerary.city.country}</p>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={() => router.push(`/itineraries/${itinerary._id}`)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={saving}
                className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <CheckIcon className="h-4 w-4" />
                    <span>Save Changes</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Day Navigation Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm p-6 sticky top-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Days</h2>
              <div className="space-y-2">
                {itinerary.days.map((day) => (
                  <button
                    key={day.dayNumber}
                    onClick={() => setActiveDay(day.dayNumber)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      activeDay === day.dayNumber
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <div className="font-medium">Day {day.dayNumber}</div>
                    <div className={`text-sm ${
                      activeDay === day.dayNumber ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {new Date(day.date).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric' 
                      })}
                    </div>
                    <div className={`text-xs ${
                      activeDay === day.dayNumber ? 'text-blue-200' : 'text-gray-400'
                    }`}>
                      {day.places.length} places
                    </div>
                  </button>
                ))}
              </div>

              {/* Add Place Button */}
              <button
                onClick={() => setShowAddPlace(true)}
                className="w-full mt-4 p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-400 hover:text-blue-600 transition-colors"
              >
                <PlusIcon className="h-5 w-5 mx-auto mb-1" />
                <span className="text-sm font-medium">Add Place</span>
              </button>
            </div>
          </div>

          {/* Day Details */}
          <div className="lg:col-span-3">
            {itinerary.days.map((day, dayIndex) => (
              activeDay === day.dayNumber && (
                <div key={day.dayNumber} className="bg-white rounded-xl shadow-sm">
                  {/* Day Header */}
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-t-xl">
                    <h2 className="text-2xl font-bold">Day {day.dayNumber}</h2>
                    <p className="text-blue-100">
                      {new Date(day.date).toLocaleDateString('en-US', { 
                        weekday: 'long', 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })}
                    </p>
                  </div>

                  <div className="p-6">
                    {/* Places List */}
                    <div className="space-y-4 mb-6">
                      {day.places.length > 0 ? (
                        day.places
                          .sort((a, b) => a.order - b.order)
                          .map((placeActivity, placeIndex) => (
                          <div key={placeIndex} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-start space-x-4">
                              {/* Place Image */}
                              <img
                                src={placeActivity.place.images?.[0]?.url || getFallbackImage(placeActivity.place.category)}
                                alt={placeActivity.place.name}
                                className="w-20 h-20 rounded-lg object-cover flex-shrink-0"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = getFallbackImage(placeActivity.place.category);
                                }}
                              />

                              {/* Place Details */}
                              <div className="flex-1">
                                <div className="flex items-start justify-between">
                                  <div>
                                    <h3 className="font-semibold text-gray-900">{placeActivity.place.name}</h3>
                                    <p className="text-sm text-gray-600 capitalize">{placeActivity.place.category}</p>
                                  </div>
                                  
                                  {/* Order Controls */}
                                  <div className="flex items-center space-x-1">
                                    <button
                                      onClick={() => movePlaceOrder(dayIndex, placeIndex, 'up')}
                                      disabled={placeIndex === 0}
                                      className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                                      title="Move up"
                                    >
                                      <ArrowUpIcon className="h-4 w-4" />
                                    </button>
                                    <button
                                      onClick={() => movePlaceOrder(dayIndex, placeIndex, 'down')}
                                      disabled={placeIndex === day.places.length - 1}
                                      className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                                      title="Move down"
                                    >
                                      <ArrowDownIcon className="h-4 w-4" />
                                    </button>
                                    <button
                                      onClick={() => removePlaceFromDay(dayIndex, placeIndex)}
                                      className="p-1 text-red-400 hover:text-red-600"
                                      title="Remove place"
                                    >
                                      <TrashIcon className="h-4 w-4" />
                                    </button>
                                  </div>
                                </div>

                                {/* Activity Details */}
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
                                  <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">Time Slot</label>
                                    <select
                                      value={placeActivity.timeSlot}
                                      onChange={(e) => updatePlaceActivity(dayIndex, placeIndex, { timeSlot: e.target.value as any })}
                                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                      <option value="morning">🌅 Morning</option>
                                      <option value="afternoon">☀️ Afternoon</option>
                                      <option value="evening">🌆 Evening</option>
                                      <option value="night">🌙 Night</option>
                                    </select>
                                  </div>

                                  <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">Duration (hours)</label>
                                    <input
                                      type="number"
                                      min="0.5"
                                      max="12"
                                      step="0.5"
                                      value={placeActivity.duration || 2}
                                      onChange={(e) => updatePlaceActivity(dayIndex, placeIndex, { duration: parseFloat(e.target.value) })}
                                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                  </div>

                                  <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">Order</label>
                                    <input
                                      type="number"
                                      min="1"
                                      value={placeActivity.order}
                                      onChange={(e) => updatePlaceActivity(dayIndex, placeIndex, { order: parseInt(e.target.value) })}
                                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                  </div>
                                </div>

                                {/* Notes */}
                                <div className="mt-3">
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Notes</label>
                                  <textarea
                                    value={placeActivity.notes || ''}
                                    onChange={(e) => updatePlaceActivity(dayIndex, placeIndex, { notes: e.target.value })}
                                    rows={2}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                    placeholder="Add notes about this visit..."
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <CalendarDaysIcon className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                          <p>No places added to this day yet.</p>
                          <button
                            onClick={() => setShowAddPlace(true)}
                            className="mt-2 text-blue-600 hover:text-blue-700 font-medium"
                          >
                            Add your first place
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Day Notes */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Day Notes</label>
                      <textarea
                        value={day.notes || ''}
                        onChange={(e) => updateDayNotes(dayIndex, e.target.value)}
                        rows={3}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                        placeholder="Add general notes for this day (transport, tips, reminders, etc.)"
                      />
                    </div>
                  </div>
                </div>
              )
            ))}
          </div>
        </div>
      </div>

      {/* Add Place Modal */}
      {showAddPlace && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Add Place to Day {activeDay}</h3>
                <button
                  onClick={() => {
                    setShowAddPlace(false);
                    setSearchQuery('');
                    setSearchResults([]);
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>
              
              {/* Search */}
              <div className="mt-4 relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={`Search places in ${itinerary.city.name}...`}
                />
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-96">
              {searchLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-600 mt-2">Searching places...</p>
                </div>
              ) : searchResults.length > 0 ? (
                <div className="space-y-3">
                  {searchResults.map((place) => (
                    <div key={place._id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start space-x-4">
                        <img
                          src={place.images?.[0]?.url || getFallbackImage(place.category)}
                          alt={place.name}
                          className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = getFallbackImage(place.category);
                          }}
                        />
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900">{place.name}</h4>
                          <p className="text-sm text-gray-600 capitalize">{place.category}</p>
                          {place.description && (
                            <p className="text-sm text-gray-500 mt-1 line-clamp-2">{place.description}</p>
                          )}
                          
                          {/* Time Slot Buttons */}
                          <div className="flex flex-wrap gap-2 mt-3">
                            {['morning', 'afternoon', 'evening', 'night'].map((timeSlot) => (
                              <button
                                key={timeSlot}
                                onClick={() => addPlaceToDay(place, timeSlot)}
                                className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium hover:bg-blue-200 transition-colors capitalize"
                              >
                                {getTimeSlotIcon(timeSlot)} {timeSlot}
                              </button>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : searchQuery ? (
                <div className="text-center py-8 text-gray-500">
                  <MagnifyingGlassIcon className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                  <p>No places found for "{searchQuery}"</p>
                  <p className="text-sm">Try searching with different keywords</p>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <MapPinIcon className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                  <p>Search for places to add to your itinerary</p>
                  <p className="text-sm">Type in the search box above to get started</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
