const fs = require('fs');

// Function to fix a file
function fixFile(filePath, replacements) {
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  replacements.forEach(({ from, to }) => {
    if (content.includes(from)) {
      content = content.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
      modified = true;
      console.log(`Fixed in ${filePath}: ${from} -> ${to}`);
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated: ${filePath}`);
  }
}

// Fix remaining critical errors (unescaped entities)
const fixes = [
  // Itineraries edit page
  {
    file: 'src/app/itineraries/[id]/edit/page.tsx',
    replacements: [
      { from: '"Edit Itinerary"', to: '&quot;Edit Itinerary&quot;' }
    ]
  },
  
  // Itineraries view page
  {
    file: 'src/app/itineraries/[id]/page.tsx',
    replacements: [
      { from: "Traveler's Notes", to: "Traveler&apos;s Notes" }
    ]
  },
  
  // Home page
  {
    file: 'src/app/page.tsx',
    replacements: [
      { from: "Discover the world's most beautiful destinations", to: "Discover the world&apos;s most beautiful destinations" }
    ]
  },
  
  // Search page
  {
    file: 'src/app/search/page.tsx',
    replacements: [
      { from: 'We couldn\'t find any cities matching "', to: 'We couldn&apos;t find any cities matching &quot;' },
      { from: '". Try searching for one of our available destinations.', to: '&quot;. Try searching for one of our available destinations.' }
    ]
  },
  
  // Visited page
  {
    file: 'src/app/visited/page.tsx',
    replacements: [
      { from: "You haven't visited any places yet.", to: "You haven&apos;t visited any places yet." },
      { from: "You haven't added any places to your visited list yet.", to: "You haven&apos;t added any places to your visited list yet." }
    ]
  },
  
  // SearchBar component
  {
    file: 'src/components/ui/SearchBar.tsx',
    replacements: [
      { from: 'placeholder="Search cities, places..."', to: 'placeholder=&quot;Search cities, places...&quot;' },
      { from: 'placeholder="Search places..."', to: 'placeholder=&quot;Search places...&quot;' }
    ]
  },
  
  // AddVisitedPlaceModal component
  {
    file: 'src/components/visited/AddVisitedPlaceModal.tsx',
    replacements: [
      { from: 'placeholder="Search for a place..."', to: 'placeholder=&quot;Search for a place...&quot;' }
    ]
  }
];

console.log('Fixing final lint errors...');
fixes.forEach(({ file, replacements }) => {
  fixFile(file, replacements);
});

console.log('Done!');
