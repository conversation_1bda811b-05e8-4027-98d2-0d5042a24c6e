import { Response } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { AdminAuthRequest } from './adminAuthController';

// Import models from the index to ensure they're registered
import { City, Place } from '../models';

// @desc    Get all cities with pagination and filtering
// @route   GET /api/admin/cities
// @access  Private (Admin)
export const getCities = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const search = req.query.search as string;
  const country = req.query.country as string;
  const isPublished = req.query.isPublished as string;
  const isFeatured = req.query.isFeatured as string;
  const sortBy = req.query.sortBy as string || 'createdAt';
  const sortOrder = req.query.sortOrder as string || 'desc';

  // Build filter object
  const filter: any = {};

  if (search) {
    filter.$or = [
      { name: { $regex: search, $options: 'i' } },
      { country: { $regex: search, $options: 'i' } }
    ];
  }

  if (country) {
    filter.country = { $regex: country, $options: 'i' };
  }

  if (isPublished !== undefined) {
    filter.isPublished = isPublished === 'true';
  }

  if (isFeatured !== undefined) {
    filter.isFeatured = isFeatured === 'true';
  }

  // Build sort object
  const sort: any = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // Calculate pagination
  const skip = (page - 1) * limit;

  // Get cities with pagination
  const cities = await City.find(filter)
    .sort(sort)
    .skip(skip)
    .limit(limit);

  // Get total count for pagination
  const total = await City.countDocuments(filter);
  const pages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: {
      items: cities,
      pagination: {
        page,
        limit,
        total,
        pages
      }
    }
  });
});

// @desc    Get single city
// @route   GET /api/admin/cities/:id
// @access  Private (Admin)
export const getCity = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const city = await City.findById(req.params.id);

  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  // Get places count for this city
  const placesCount = await Place.countDocuments({ city: city._id, isPublished: true });

  res.json({
    success: true,
    data: {
      ...city.toObject(),
      placesCount
    }
  });
});

// @desc    Create new city
// @route   POST /api/admin/cities
// @access  Private (Admin)
export const createCity = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const {
    name,
    country,
    description,
    overview,
    coordinates,
    timezone,
    currency,
    languages,
    images,
    isPublished,
    isFeatured,
    seoMetadata
  } = req.body;

  // Check if city already exists
  const existingCity = await City.findOne({ 
    name: { $regex: new RegExp(`^${name}$`, 'i') },
    country: { $regex: new RegExp(`^${country}$`, 'i') }
  });

  if (existingCity) {
    return res.status(400).json({
      success: false,
      error: 'City already exists in this country'
    });
  }

  // Create slug
  const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

  const city = await City.create({
    name,
    slug,
    country,
    description,
    overview,
    coordinates,
    timezone,
    currency,
    languages: languages || [],
    images: images || [],
    isPublished: isPublished || false,
    isFeatured: isFeatured || false,
    seoMetadata
  });

  res.status(201).json({
    success: true,
    data: city
  });
});

// @desc    Update city
// @route   PUT /api/admin/cities/:id
// @access  Private (Admin)
export const updateCity = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const city = await City.findById(req.params.id);

  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  // Update slug if name changed
  if (req.body.name && req.body.name !== city.name) {
    req.body.slug = req.body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
  }

  const updatedCity = await City.findByIdAndUpdate(
    req.params.id,
    req.body,
    { new: true, runValidators: true }
  );

  res.json({
    success: true,
    data: updatedCity
  });
});

// @desc    Delete city
// @route   DELETE /api/admin/cities/:id
// @access  Private (Super Admin)
export const deleteCity = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const city = await City.findById(req.params.id);

  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  // Check if city has places
  const placesCount = await Place.countDocuments({ city: city._id });
  if (placesCount > 0) {
    return res.status(400).json({
      success: false,
      error: `Cannot delete city. It has ${placesCount} places. Please delete or move the places first.`
    });
  }

  await City.findByIdAndDelete(req.params.id);

  res.json({
    success: true,
    message: 'City deleted successfully'
  });
});

// @desc    Toggle city published status
// @route   PATCH /api/admin/cities/:id/toggle-status
// @access  Private (Admin)
export const toggleCityStatus = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const city = await City.findById(req.params.id);

  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  city.isPublished = !city.isPublished;
  await city.save();

  res.json({
    success: true,
    data: city,
    message: `City ${city.isPublished ? 'published' : 'unpublished'} successfully`
  });
});

// @desc    Toggle city featured status
// @route   PATCH /api/admin/cities/:id/toggle-featured
// @access  Private (Admin)
export const toggleCityFeatured = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const city = await City.findById(req.params.id);

  if (!city) {
    return res.status(404).json({
      success: false,
      error: 'City not found'
    });
  }

  city.isFeatured = !city.isFeatured;
  await city.save();

  res.json({
    success: true,
    data: city,
    message: `City ${city.isFeatured ? 'featured' : 'unfeatured'} successfully`
  });
});

// @desc    Bulk delete cities
// @route   POST /api/admin/cities/bulk-delete
// @access  Private (Super Admin)
export const bulkDeleteCities = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const { ids } = req.body;

  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Please provide an array of city IDs'
    });
  }

  // Check if any cities have places
  const citiesWithPlaces = await Place.aggregate([
    { $match: { city: { $in: ids } } },
    { $group: { _id: '$city', count: { $sum: 1 } } }
  ]);

  if (citiesWithPlaces.length > 0) {
    return res.status(400).json({
      success: false,
      error: 'Cannot delete cities that have places. Please delete or move the places first.'
    });
  }

  const result = await City.deleteMany({ _id: { $in: ids } });

  res.json({
    success: true,
    message: `${result.deletedCount} cities deleted successfully`
  });
});

// @desc    Get city statistics
// @route   GET /api/admin/cities/stats
// @access  Private (Admin)
export const getCityStats = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const totalCities = await City.countDocuments();
  const publishedCities = await City.countDocuments({ isPublished: true });
  const featuredCities = await City.countDocuments({ isFeatured: true });
  
  // Cities created in the last 30 days
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const recentCities = await City.countDocuments({ 
    createdAt: { $gte: thirtyDaysAgo } 
  });

  // Cities by country
  const citiesByCountry = await City.aggregate([
    {
      $group: {
        _id: '$country',
        count: { $sum: 1 }
      }
    },
    { $sort: { count: -1 } },
    { $limit: 10 }
  ]);

  res.json({
    success: true,
    data: {
      totalCities,
      publishedCities,
      featuredCities,
      recentCities,
      citiesByCountry
    }
  });
});
