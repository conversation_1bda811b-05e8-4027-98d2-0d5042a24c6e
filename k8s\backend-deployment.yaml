apiVersion: apps/v1
kind: Deployment
metadata:
  name: heritedge-backend-deployment
  namespace: heritedge
  labels:
    app: heritedge-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: heritedge-backend
  template:
    metadata:
      labels:
        app: heritedge-backend
    spec:
      containers:
      - name: heritedge-backend
        image: heritedge/backend:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5000
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: heritedge-config
              key: NODE_ENV
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: heritedge-config
              key: PORT
        - name: MONGODB_URI
          valueFrom:
            configMapKeyRef:
              name: heritedge-config
              key: MONGODB_URI
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: heritedge-secrets
              key: JWT_SECRET
        - name: JWT_REFRESH_SECRET
          valueFrom:
            secretKeyRef:
              name: heritedge-secrets
              key: JWT_REFRESH_SECRET
        - name: JWT_EXPIRE
          valueFrom:
            configMapKeyRef:
              name: heritedge-config
              key: JWT_EXPIRE
        - name: JWT_REFRESH_EXPIRE
          valueFrom:
            configMapKeyRef:
              name: heritedge-config
              key: JWT_REFRESH_EXPIRE
        - name: FRONTEND_URL
          valueFrom:
            configMapKeyRef:
              name: heritedge-config
              key: FRONTEND_URL
        - name: ADMIN_URL
          valueFrom:
            configMapKeyRef:
              name: heritedge-config
              key: ADMIN_URL
        volumeMounts:
        - name: uploads-storage
          mountPath: /app/uploads
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: uploads-storage
        persistentVolumeClaim:
          claimName: backend-uploads-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: heritedge-backend-service
  namespace: heritedge
  labels:
    app: heritedge-backend
spec:
  selector:
    app: heritedge-backend
  ports:
  - port: 5000
    targetPort: 5000
    protocol: TCP
    name: http
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  name: heritedge-backend-nodeport
  namespace: heritedge
  labels:
    app: heritedge-backend
spec:
  selector:
    app: heritedge-backend
  ports:
  - port: 5000
    targetPort: 5000
    nodePort: 30001
    protocol: TCP
    name: http
  type: NodePort
