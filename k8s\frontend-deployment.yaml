apiVersion: apps/v1
kind: Deployment
metadata:
  name: heritedge-frontend-deployment
  namespace: heritedge
  labels:
    app: heritedge-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: heritedge-frontend
  template:
    metadata:
      labels:
        app: heritedge-frontend
    spec:
      containers:
      - name: heritedge-frontend
        image: heritedge/frontend:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: NEXT_PUBLIC_API_URL
          valueFrom:
            configMapKeyRef:
              name: heritedge-config
              key: NEXT_PUBLIC_API_URL
        - name: NEXT_PUBLIC_FRONTEND_URL
          valueFrom:
            configMapKeyRef:
              name: heritedge-config
              key: NEXT_PUBLIC_FRONTEND_URL
        - name: NEXT_TELEMETRY_DISABLED
          value: "1"
        - name: PORT
          value: "3000"
        - name: HOSTNAME
          value: "0.0.0.0"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3

---
apiVersion: v1
kind: Service
metadata:
  name: heritedge-frontend-service
  namespace: heritedge
  labels:
    app: heritedge-frontend
spec:
  selector:
    app: heritedge-frontend
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  name: heritedge-frontend-nodeport
  namespace: heritedge
  labels:
    app: heritedge-frontend
spec:
  selector:
    app: heritedge-frontend
  ports:
  - port: 3000
    targetPort: 3000
    nodePort: 30000
    protocol: TCP
    name: http
  type: NodePort

