# HeritEdge Kubernetes Deployment Script (PowerShell)
Write-Host "🚀 Deploying HeritEdge to Kubernetes..." -ForegroundColor Blue

# Function to print colored output
function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if kubectl is available
try {
    kubectl version --client | Out-Null
} catch {
    Write-Error "kubectl is not installed. Please install kubectl and try again."
    exit 1
}

# Check if Kubernetes cluster is accessible
try {
    kubectl cluster-info | Out-Null
    Write-Status "Kubernetes cluster is accessible ✓"
} catch {
    Write-Error "Cannot connect to Kubernetes cluster. Please check your cluster connection."
    exit 1
}

# Create namespace
Write-Status "Creating namespace..."
kubectl apply -f k8s/namespace.yaml

# Apply ConfigMaps and Secrets
Write-Status "Applying ConfigMaps and Secrets..."
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml

# Apply Persistent Volumes
Write-Status "Creating Persistent Volumes..."
kubectl apply -f k8s/persistent-volumes.yaml

# Wait for PVCs to be bound
Write-Status "Waiting for Persistent Volume Claims to be bound..."
kubectl wait --for=condition=Bound pvc/mongodb-pvc -n heritedge --timeout=60s
kubectl wait --for=condition=Bound pvc/backend-uploads-pvc -n heritedge --timeout=60s

# Deploy MongoDB
Write-Status "Deploying MongoDB..."
kubectl apply -f k8s/mongodb-deployment.yaml

# Wait for MongoDB to be ready
Write-Status "Waiting for MongoDB to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/mongodb-deployment -n heritedge

# Deploy Backend
Write-Status "Deploying Backend..."
kubectl apply -f k8s/backend-deployment.yaml

# Wait for Backend to be ready
Write-Status "Waiting for Backend to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/heritedge-backend-deployment -n heritedge

# Deploy Frontend
Write-Status "Deploying Frontend..."
kubectl apply -f k8s/frontend-deployment.yaml

# Wait for Frontend to be ready
Write-Status "Waiting for Frontend to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/heritedge-frontend-deployment -n heritedge

# Deploy Admin
Write-Status "Deploying Admin..."
kubectl apply -f k8s/admin-deployment.yaml

# Wait for Admin to be ready
Write-Status "Waiting for Admin to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/heritedge-admin-deployment -n heritedge

# Get service information
Write-Success "Deployment completed successfully! 🎉"
Write-Status "Getting service information..."

Write-Host ""
Write-Status "📋 Service Status:"
kubectl get pods -n heritedge
Write-Host ""
kubectl get services -n heritedge
Write-Host ""

# Get NodePort information
$FRONTEND_NODEPORT = kubectl get service heritedge-frontend-nodeport -n heritedge -o jsonpath='{.spec.ports[0].nodePort}'
$ADMIN_NODEPORT = kubectl get service heritedge-admin-nodeport -n heritedge -o jsonpath='{.spec.ports[0].nodePort}'
$BACKEND_NODEPORT = kubectl get service heritedge-backend-nodeport -n heritedge -o jsonpath='{.spec.ports[0].nodePort}'

Write-Success "🌐 Access URLs:"
Write-Host "Frontend: http://localhost:$FRONTEND_NODEPORT"
Write-Host "Admin: http://localhost:$ADMIN_NODEPORT"
Write-Host "Backend API: http://localhost:$BACKEND_NODEPORT"
Write-Host ""

Write-Success "🔐 Default Admin Credentials:"
Write-Host "Email: <EMAIL>"
Write-Host "Password: admin123456"
Write-Host ""

Write-Warning "⚠️  Important Security Notes:"
Write-Host "1. Change default admin password after first login"
Write-Host "2. Update JWT secrets in production"
Write-Host "3. Use proper TLS certificates for production deployment"
Write-Host ""

Write-Status "📊 To monitor the deployment:"
Write-Host "kubectl get pods -n heritedge -w"
Write-Host "kubectl logs -f deployment/heritedge-backend-deployment -n heritedge"
Write-Host ""

Write-Status "🗑️  To clean up the deployment:"
Write-Host ".\scripts\cleanup-k8s.ps1"
