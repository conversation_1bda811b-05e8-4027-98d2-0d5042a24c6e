'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { apiClient } from '@/lib/api';
import { VisitedPlacesResponse, VisitedPlaceFilters } from '@/types';
import { Breadcrumb } from '@/components/ui/Breadcrumb';
import { VisitedPlaceCard } from '@/components/visited/VisitedPlaceCard';
import { VisitedPlacesFilters } from '@/components/visited/VisitedPlacesFilters';
import { VisitedPlacesStats } from '@/components/visited/VisitedPlacesStats';
import { AddVisitedPlaceModal } from '@/components/visited/AddVisitedPlaceModal';
import { 
  MapPinIcon, 
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ViewColumnsIcon,
  Squares2X2Icon
} from '@heroicons/react/24/outline';

export default function VisitedPlacesPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [data, setData] = useState<VisitedPlacesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<VisitedPlaceFilters>({
    page: 1,
    limit: 20,
    sortBy: 'visitDate',
    sortOrder: 'desc'
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch visited places
  const fetchVisitedPlaces = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getVisitedPlaces(filters);
      if (response.success) {
        setData(response.data);
      } else {
        setError('Failed to load visited places');
      }
    } catch (err: any) {
      console.error('Error fetching visited places:', err);
      setError(err.message || 'Failed to load visited places');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchVisitedPlaces();
    }
  }, [isAuthenticated, filters, fetchVisitedPlaces]);

  const handleFilterChange = (newFilters: Partial<VisitedPlaceFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handlePlaceAdded = () => {
    setShowAddModal(false);
    fetchVisitedPlaces(); // Refresh the list
  };

  const handlePlaceUpdated = () => {
    fetchVisitedPlaces(); // Refresh the list
  };

  const handlePlaceRemoved = () => {
    fetchVisitedPlaces(); // Refresh the list
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <MapPinIcon className="h-8 w-8 text-blue-600 mr-3" />
                My Visited Places
              </h1>
              <p className="mt-2 text-gray-600">
                Keep track of all the amazing places you&apos;ve visited
              </p>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <PlusIcon className="h-5 w-5" />
              <span>Add Place</span>
            </button>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <Breadcrumb items={[
            { label: 'Dashboard', href: '/dashboard' },
            { label: 'Visited Places', isActive: true }
          ]} />
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        {data?.stats && (
          <div className="mb-8">
            <VisitedPlacesStats stats={data.stats} />
          </div>
        )}

        {/* Filters and Controls */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
              <div className="relative">
                <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search places..."
                  value={filters.search || ''}
                  onChange={(e) => handleFilterChange({ search: e.target.value })}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                <FunnelIcon className="h-5 w-5" />
                <span>Filters</span>
              </button>
            </div>

            {/* View Controls */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded ${viewMode === 'grid' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
                >
                  <Squares2X2Icon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded ${viewMode === 'list' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
                >
                  <ViewColumnsIcon className="h-4 w-4" />
                </button>
              </div>
              {data && (
                <span className="text-sm text-gray-600">
                  {data.pagination.total} places
                </span>
              )}
            </div>
          </div>

          {/* Expandable Filters */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <VisitedPlacesFilters
                filters={filters}
                onFilterChange={handleFilterChange}
              />
            </div>
          )}
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <p className="text-red-600">{error}</p>
            <button
              onClick={fetchVisitedPlaces}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        ) : !data?.visitedPlaces.length ? (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <MapPinIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No visited places yet</h3>
            <p className="text-gray-600 mb-6">
              Start tracking your travels by adding places you&apos;ve visited
            </p>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Add Your First Place
            </button>
          </div>
        ) : (
          <div className={`${viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' 
            : 'space-y-4'
          }`}>
            {data.visitedPlaces.map((visitedPlace) => (
              <VisitedPlaceCard
                key={`${visitedPlace.place.id}-${visitedPlace.visitDate}`}
                visitedPlace={visitedPlace}
                viewMode={viewMode}
                onUpdate={handlePlaceUpdated}
                onRemove={handlePlaceRemoved}
              />
            ))}
          </div>
        )}

        {/* Pagination */}
        {data && data.pagination.pages > 1 && (
          <div className="mt-8 flex justify-center">
            <div className="flex space-x-2">
              {Array.from({ length: data.pagination.pages }, (_, i) => i + 1).map((page) => (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={`px-4 py-2 rounded-lg ${
                    page === data.pagination.page
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                  }`}
                >
                  {page}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Add Visited Place Modal */}
      {showAddModal && (
        <AddVisitedPlaceModal
          onClose={() => setShowAddModal(false)}
          onPlaceAdded={handlePlaceAdded}
        />
      )}
    </div>
  );
}
