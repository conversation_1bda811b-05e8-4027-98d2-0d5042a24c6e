import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse, PaginatedResponse } from '@/types';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.removeToken();
          // Redirect to login if needed
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  private getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('token');
  }

  private setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('token', token);
    }
  }

  private removeToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
    }
  }

  // Auth endpoints
  async login(email: string, password: string): Promise<ApiResponse<any>> {
    const response = await this.client.post('/auth/login', { email, password });
    // Don't set token here, let the AuthContext handle it
    return response.data;
  }

  async register(name: string, email: string, password: string): Promise<ApiResponse<any>> {
    const response = await this.client.post('/auth/register', { name, email, password });
    // Don't set token here, let the AuthContext handle it
    return response.data;
  }

  async logout(): Promise<ApiResponse<any>> {
    const response = await this.client.post('/auth/logout');
    this.removeToken();
    return response.data;
  }

  async getCurrentUser(): Promise<ApiResponse<any>> {
    const response = await this.client.get('/auth/me');
    return response.data;
  }

  // Set auth token manually
  setAuthToken(token: string | null): void {
    if (token) {
      this.setToken(token);
    } else {
      this.removeToken();
    }
  }

  async updateProfile(data: any): Promise<ApiResponse<any>> {
    const response = await this.client.put('/auth/profile', data);
    return response.data;
  }

  // Cities endpoints
  async getCities(params?: any): Promise<PaginatedResponse<any>> {
    const response = await this.client.get('/cities', { params });
    return response.data;
  }

  async getCity(slug: string): Promise<ApiResponse<any>> {
    const response = await this.client.get(`/cities/${slug}`);
    return response.data;
  }

  async getFeaturedCities(): Promise<ApiResponse<any>> {
    const response = await this.client.get('/cities/featured');
    return response.data;
  }

  async getGlobalStats(): Promise<ApiResponse<any>> {
    const response = await this.client.get('/cities/stats/global');
    return response.data;
  }

  async createCity(data: any): Promise<ApiResponse<any>> {
    const response = await this.client.post('/cities', data);
    return response.data;
  }

  async updateCity(slug: string, data: any): Promise<ApiResponse<any>> {
    const response = await this.client.put(`/cities/${slug}`, data);
    return response.data;
  }

  async deleteCity(slug: string): Promise<ApiResponse<any>> {
    const response = await this.client.delete(`/cities/${slug}`);
    return response.data;
  }

  async getCityPlaces(slug: string, params?: any): Promise<PaginatedResponse<any>> {
    const response = await this.client.get(`/cities/${slug}/places`, { params });
    return response.data;
  }

  // Get all places with filtering
  async getAllPlaces(params?: {
    page?: number;
    limit?: number;
    category?: string;
    city?: string;
    search?: string;
  }): Promise<PaginatedResponse<any>> {
    const response = await this.client.get('/places', { params });
    return response.data;
  }

  // Get single place by slug
  // Removed duplicate getPlace method to resolve duplicate implementation error.

  async bookmarkCity(slug: string): Promise<ApiResponse<any>> {
    const response = await this.client.post(`/cities/${slug}/bookmark`);
    return response.data;
  }

  async unbookmarkCity(slug: string): Promise<ApiResponse<any>> {
    const response = await this.client.delete(`/cities/${slug}/bookmark`);
    return response.data;
  }

  // Places endpoints
  async getPlaces(params?: any): Promise<PaginatedResponse<any>> {
    const response = await this.client.get('/places', { params });
    return response.data;
  }

  async getPlace(slug: string): Promise<ApiResponse<any>> {
    const response = await this.client.get(`/places/${slug}`);
    return response.data;
  }

  async getNearbyPlaces(params: any): Promise<ApiResponse<any>> {
    const response = await this.client.get('/places/nearby', { params });
    return response.data;
  }

  async createPlace(data: any): Promise<ApiResponse<any>> {
    const response = await this.client.post('/places', data);
    return response.data;
  }

  async updatePlace(slug: string, data: any): Promise<ApiResponse<any>> {
    const response = await this.client.put(`/places/${slug}`, data);
    return response.data;
  }

  async deletePlace(slug: string): Promise<ApiResponse<any>> {
    const response = await this.client.delete(`/places/${slug}`);
    return response.data;
  }

  // Generic review methods for both cities and places
  async getReviews(entityType: 'city' | 'place', slug: string): Promise<ApiResponse<any>> {
    const endpoint = entityType === 'city' ? `/cities/${slug}/reviews` : `/places/${slug}/reviews`;
    const response = await this.client.get(endpoint);
    return response.data;
  }

  async addReview(entityType: 'city' | 'place', slug: string, data: any): Promise<ApiResponse<any>> {
    const endpoint = entityType === 'city' ? `/cities/${slug}/reviews` : `/places/${slug}/reviews`;
    const response = await this.client.post(endpoint, data);
    return response.data;
  }

  async updateReview(entityType: 'city' | 'place', slug: string, reviewId: string, data: any): Promise<ApiResponse<any>> {
    const endpoint = entityType === 'city' ? `/cities/${slug}/reviews/${reviewId}` : `/places/${slug}/reviews/${reviewId}`;
    const response = await this.client.put(endpoint, data);
    return response.data;
  }

  async deleteReview(entityType: 'city' | 'place', slug: string, reviewId: string): Promise<ApiResponse<any>> {
    const endpoint = entityType === 'city' ? `/cities/${slug}/reviews/${reviewId}` : `/places/${slug}/reviews/${reviewId}`;
    const response = await this.client.delete(endpoint);
    return response.data;
  }

  async bookmarkPlace(slug: string): Promise<ApiResponse<any>> {
    const response = await this.client.post(`/places/${slug}/bookmark`);
    return response.data;
  }

  async unbookmarkPlace(slug: string): Promise<ApiResponse<any>> {
    const response = await this.client.delete(`/places/${slug}/bookmark`);
    return response.data;
  }

  // Search endpoints
  async search(params: any): Promise<ApiResponse<any>> {
    const response = await this.client.get('/search', { params });
    return response.data;
  }

  async searchCities(query: string): Promise<ApiResponse<any>> {
    const response = await this.client.get('/search/cities', { params: { q: query } });
    return response.data;
  }

  async searchPlaces(query: string): Promise<ApiResponse<any>> {
    const response = await this.client.get('/search/places', { params: { q: query } });
    return response.data;
  }

  async searchItineraries(query: string): Promise<ApiResponse<any>> {
    const response = await this.client.get('/search/itineraries', { params: { q: query } });
    return response.data;
  }

  async getSearchSuggestions(params: any): Promise<ApiResponse<any>> {
    const response = await this.client.get('/search/suggestions', { params });
    return response.data;
  }

  async getTrendingSearches(params?: any): Promise<ApiResponse<any>> {
    const response = await this.client.get('/search/trending', { params });
    return response.data;
  }

  // Users endpoints
  async getUserProfile(userId: string): Promise<ApiResponse<any>> {
    const response = await this.client.get(`/users/${userId}`);
    return response.data;
  }

  async getUserBookmarksById(userId: string, params?: any): Promise<ApiResponse<any>> {
    const response = await this.client.get(`/users/${userId}/bookmarks`, { params });
    return response.data;
  }

  async getUserContributions(userId: string, params?: any): Promise<ApiResponse<any>> {
    const response = await this.client.get(`/users/${userId}/contributions`, { params });
    return response.data;
  }

  async updateUserPreferences(userId: string, data: any): Promise<ApiResponse<any>> {
    const response = await this.client.put(`/users/${userId}/preferences`, data);
    return response.data;
  }

  // Dashboard and personalization endpoints
  async getDashboard(): Promise<ApiResponse<any>> {
    const response = await this.client.get('/users/dashboard');
    return response.data;
  }

  async togglePlaceBookmark(placeId: string): Promise<ApiResponse<any>> {
    const response = await this.client.post(`/users/bookmark/place/${placeId}`);
    return response.data;
  }

  async togglePlaceLike(placeId: string): Promise<ApiResponse<any>> {
    const response = await this.client.post(`/users/like/place/${placeId}`);
    return response.data;
  }

  // Itinerary endpoints
  async getItineraries(params?: any): Promise<PaginatedResponse<any>> {
    const response = await this.client.get('/itineraries', { params });
    return response.data;
  }

  async getItinerary(id: string): Promise<ApiResponse<any>> {
    const response = await this.client.get(`/itineraries/${id}`);
    return response.data;
  }

  async createItinerary(data: any): Promise<ApiResponse<any>> {
    const response = await this.client.post('/itineraries', data);
    return response.data;
  }

  async updateItinerary(id: string, data: any): Promise<ApiResponse<any>> {
    const response = await this.client.put(`/itineraries/${id}`, data);
    return response.data;
  }

  async deleteItinerary(id: string): Promise<ApiResponse<any>> {
    const response = await this.client.delete(`/itineraries/${id}`);
    return response.data;
  }

  async getUserItineraries(userId: string, params?: any): Promise<PaginatedResponse<any>> {
    const response = await this.client.get(`/itineraries/user/${userId}`, { params });
    return response.data;
  }

  async toggleItineraryLike(id: string): Promise<ApiResponse<any>> {
    const response = await this.client.post(`/itineraries/${id}/like`);
    return response.data;
  }

  // Get user bookmarks
  async getUserBookmarks(type: 'places' | 'cities' | 'likes', params?: any): Promise<PaginatedResponse<any>> {
    const response = await this.client.get('/users/bookmarks', {
      params: { type, ...params }
    });
    return response.data;
  }

  // Toggle city bookmark
  async toggleCityBookmark(cityId: string): Promise<ApiResponse<any>> {
    const response = await this.client.post(`/users/bookmark/city/${cityId}`);
    return response.data;
  }

  // Check if place is bookmarked/liked
  async getPlaceStatus(placeId: string): Promise<ApiResponse<any>> {
    const response = await this.client.get(`/users/place-status/${placeId}`);
    return response.data;
  }

  // Check if city is bookmarked
  async getCityStatus(cityId: string): Promise<ApiResponse<any>> {
    const response = await this.client.get(`/users/city-status/${cityId}`);
    return response.data;
  }

  // Visited Places endpoints
  async getVisitedPlaces(params?: any): Promise<ApiResponse<any>> {
    const response = await this.client.get('/users/visited-places', { params });
    return response.data;
  }

  async addVisitedPlace(data: {
    placeId: string;
    visitDate: string;
    rating?: number;
    review?: string;
  }): Promise<ApiResponse<any>> {
    const response = await this.client.post('/users/visited-places', data);
    return response.data;
  }

  async updateVisitedPlace(placeId: string, data: {
    visitDate?: string;
    rating?: number;
    review?: string;
  }): Promise<ApiResponse<any>> {
    const response = await this.client.put(`/users/visited-places/${placeId}`, data);
    return response.data;
  }

  async removeVisitedPlace(placeId: string): Promise<ApiResponse<any>> {
    const response = await this.client.delete(`/users/visited-places/${placeId}`);
    return response.data;
  }

  async getVisitedPlaceStatus(placeId: string): Promise<ApiResponse<any>> {
    const response = await this.client.get(`/users/visited-status/${placeId}`);
    return response.data;
  }

  // Generate itinerary recommendations
  async generateItineraryRecommendations(data: any): Promise<ApiResponse<any>> {
    const response = await this.client.post('/itineraries/generate', data);
    return response.data;
  }
}

export const apiClient = new ApiClient();
export default apiClient;
