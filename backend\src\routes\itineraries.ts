import express from 'express';
import { body, query, param } from 'express-validator';
import {
  getItineraries,
  getItinerary,
  createItinerary,
  updateItinerary,
  deleteItinerary,
  getUserItineraries,
  toggleItineraryLike
} from '../controllers/itineraryController';
import { generateItinerary } from '../controllers/itineraryGeneratorController';
import { protect, optionalAuth } from '../middleware/auth';

const router = express.Router();

// @route   GET /api/itineraries
// @desc    Get all public itineraries with filtering
// @access  Public
router.get('/', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  query('city')
    .optional()
    .isMongoId()
    .withMessage('Invalid city ID'),
  query('travelStyle')
    .optional()
    .isIn(['solo', 'couple', 'family', 'group', 'business'])
    .withMessage('Invalid travel style'),
  query('duration')
    .optional()
    .isInt({ min: 1, max: 30 })
    .withMessage('Duration must be between 1 and 30 days'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('Search term must be at least 2 characters'),
  query('sort')
    .optional()
    .isIn(['recent', 'popular', 'rating'])
    .withMessage('Invalid sort parameter')
], getItineraries);

// @route   GET /api/itineraries/user/:userId
// @desc    Get user's itineraries
// @access  Private (own) / Public (if public profile)
router.get('/user/:userId', [
  param('userId')
    .isMongoId()
    .withMessage('Invalid user ID'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
], optionalAuth, getUserItineraries);

// @route   GET /api/itineraries/:id
// @desc    Get single itinerary
// @access  Public (if public) / Private (if own)
router.get('/:id', [
  param('id')
    .isMongoId()
    .withMessage('Invalid itinerary ID')
], optionalAuth, getItinerary);

// @route   POST /api/itineraries
// @desc    Create new itinerary
// @access  Private
router.post('/', protect, [
  body('title')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description cannot exceed 1000 characters'),
  body('cityId')
    .isMongoId()
    .withMessage('Valid city ID is required'),
  body('duration')
    .isInt({ min: 1, max: 30 })
    .withMessage('Duration must be between 1 and 30 days'),
  body('startDate')
    .isISO8601()
    .withMessage('Valid start date is required'),
  body('endDate')
    .optional()
    .isISO8601()
    .withMessage('Valid end date is required'),
  body('budget')
    .isIn(['budget', 'mid-range', 'luxury'])
    .withMessage('Valid budget level is required'),
  body('travelStyle')
    .isIn(['solo', 'couple', 'family', 'group', 'business'])
    .withMessage('Valid travel style is required'),
  body('interests')
    .isArray({ min: 1 })
    .withMessage('At least one interest is required'),
  body('days')
    .optional()
    .isArray()
    .withMessage('Days must be an array'),
  body('days.*.dayNumber')
    .isInt({ min: 1 })
    .withMessage('Day number must be a positive integer'),
  body('days.*.places')
    .isArray()
    .withMessage('Places must be an array'),
  body('days.*.places.*.place')
    .isMongoId()
    .withMessage('Valid place ID is required'),
  body('days.*.places.*.timeSlot')
    .isIn(['morning', 'afternoon', 'evening', 'night'])
    .withMessage('Valid time slot is required'),
  body('days.*.places.*.order')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Order must be a positive integer'),
  body('accommodationPreference')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Accommodation preference cannot exceed 100 characters'),
  body('transportPreference')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Transport preference cannot exceed 100 characters'),
  body('specialRequests')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Special requests cannot exceed 1000 characters'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('isPublic must be a boolean')
], createItinerary);

// @route   POST /api/itineraries/generate
// @desc    Generate AI-powered itinerary
// @access  Private
router.post('/generate', protect, [
  body('cityId')
    .isMongoId()
    .withMessage('Valid city ID is required'),
  body('duration')
    .isInt({ min: 1, max: 30 })
    .withMessage('Duration must be between 1 and 30 days'),
  body('startDate')
    .isISO8601()
    .withMessage('Valid start date is required'),
  body('budget')
    .isIn(['budget', 'mid-range', 'luxury'])
    .withMessage('Valid budget level is required'),
  body('travelStyle')
    .isIn(['solo', 'couple', 'family', 'group', 'business'])
    .withMessage('Valid travel style is required'),
  body('interests')
    .isArray({ min: 1 })
    .withMessage('At least one interest is required')
], generateItinerary);

// @route   PUT /api/itineraries/:id
// @desc    Update itinerary
// @access  Private (owner only)
router.put('/:id', protect, [
  param('id')
    .isMongoId()
    .withMessage('Invalid itinerary ID'),
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description cannot exceed 1000 characters'),
  body('duration')
    .optional()
    .isInt({ min: 1, max: 30 })
    .withMessage('Duration must be between 1 and 30 days'),
  body('travelStyle')
    .optional()
    .isIn(['solo', 'couple', 'family', 'group', 'business'])
    .withMessage('Valid travel style is required'),
  body('status')
    .optional()
    .isIn(['draft', 'published', 'archived'])
    .withMessage('Valid status is required'),
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('isPublic must be a boolean')
], updateItinerary);

// @route   DELETE /api/itineraries/:id
// @desc    Delete itinerary
// @access  Private (owner only)
router.delete('/:id', protect, [
  param('id')
    .isMongoId()
    .withMessage('Invalid itinerary ID')
], deleteItinerary);

// @route   POST /api/itineraries/:id/like
// @desc    Toggle itinerary like
// @access  Private
router.post('/:id/like', protect, [
  param('id')
    .isMongoId()
    .withMessage('Invalid itinerary ID')
], toggleItineraryLike);

export default router;
