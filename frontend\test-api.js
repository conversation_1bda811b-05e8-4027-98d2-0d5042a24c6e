// Simple test script to verify API responses
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5001/api';

async function testAPI() {
  console.log('Testing API endpoints...');
  console.log('API Base URL:', API_BASE_URL);

  try {
    // Test cities endpoint
    console.log('\n1. Testing /cities endpoint...');
    const citiesResponse = await axios.get(`${API_BASE_URL}/cities`);
    console.log('Cities response status:', citiesResponse.status);
    console.log('Cities response structure:', {
      success: citiesResponse.data.success,
      hasData: !!citiesResponse.data.data,
      dataKeys: citiesResponse.data.data ? Object.keys(citiesResponse.data.data) : 'No data',
      itemsCount: citiesResponse.data.data?.items?.length || 'No items array'
    });

    // Test places endpoint
    console.log('\n2. Testing /places endpoint...');
    const placesResponse = await axios.get(`${API_BASE_URL}/places`);
    console.log('Places response status:', placesResponse.status);
    console.log('Places response structure:', {
      success: placesResponse.data.success,
      hasData: !!placesResponse.data.data,
      dataKeys: placesResponse.data.data ? Object.keys(placesResponse.data.data) : 'No data',
      itemsCount: placesResponse.data.data?.items?.length || 'No items array'
    });

    // Test featured cities endpoint
    console.log('\n3. Testing /cities/featured endpoint...');
    const featuredResponse = await axios.get(`${API_BASE_URL}/cities/featured`);
    console.log('Featured cities response status:', featuredResponse.status);
    console.log('Featured cities response structure:', {
      success: featuredResponse.data.success,
      hasData: !!featuredResponse.data.data,
      dataKeys: featuredResponse.data.data ? Object.keys(featuredResponse.data.data) : 'No data',
      itemsCount: featuredResponse.data.data?.items?.length || featuredResponse.data.data?.cities?.length || 'No items/cities array'
    });

    // Test dashboard endpoint (requires authentication)
    console.log('\n4. Testing /users/dashboard endpoint...');
    try {
      const dashboardResponse = await axios.get(`${API_BASE_URL}/users/dashboard`);
      console.log('Dashboard response status:', dashboardResponse.status);
      console.log('Dashboard response structure:', {
        success: dashboardResponse.data.success,
        hasData: !!dashboardResponse.data.data,
        dataKeys: dashboardResponse.data.data ? Object.keys(dashboardResponse.data.data) : 'No data'
      });
    } catch (dashError) {
      console.log('Dashboard endpoint error (expected - requires auth):', dashError.response?.status || dashError.message);
    }

    // Test bookmarks endpoints (requires authentication)
    console.log('\n5. Testing /users/bookmarks endpoints...');
    const bookmarkTypes = ['places', 'cities', 'likes'];
    for (const type of bookmarkTypes) {
      try {
        const bookmarksResponse = await axios.get(`${API_BASE_URL}/users/bookmarks?type=${type}`);
        console.log(`Bookmarks (${type}) response status:`, bookmarksResponse.status);
        console.log(`Bookmarks (${type}) response structure:`, {
          success: bookmarksResponse.data.success,
          hasData: !!bookmarksResponse.data.data,
          dataKeys: bookmarksResponse.data.data ? Object.keys(bookmarksResponse.data.data) : 'No data'
        });
      } catch (bookmarkError) {
        console.log(`Bookmarks (${type}) endpoint error (expected - requires auth):`, bookmarkError.response?.status || bookmarkError.message);
      }
    }

    // Test itineraries endpoints (requires authentication)
    console.log('\n6. Testing /itineraries/user/:userId endpoints...');
    const testUserIds = ['507f1f77bcf86cd799439011', '507f191e810c19729de860ea']; // Sample MongoDB ObjectIds
    for (const userId of testUserIds) {
      try {
        const itinerariesResponse = await axios.get(`${API_BASE_URL}/itineraries/user/${userId}`);
        console.log(`Itineraries (${userId}) response status:`, itinerariesResponse.status);
        console.log(`Itineraries (${userId}) response structure:`, {
          success: itinerariesResponse.data.success,
          hasData: !!itinerariesResponse.data.data,
          dataKeys: itinerariesResponse.data.data ? Object.keys(itinerariesResponse.data.data) : 'No data'
        });
      } catch (itineraryError) {
        console.log(`Itineraries (${userId}) endpoint error:`, itineraryError.response?.status || itineraryError.message);
        if (itineraryError.response?.data) {
          console.log(`Error details:`, itineraryError.response.data);
        }
      }
    }

    // Test city places endpoints
    console.log('\n7. Testing /cities/:slug/places endpoints...');
    try {
      // First get a city to test with
      const citiesResponse = await axios.get(`${API_BASE_URL}/cities?limit=1`);
      if (citiesResponse.data.success && citiesResponse.data.data.cities.length > 0) {
        const firstCity = citiesResponse.data.data.cities[0];
        console.log(`Testing places for city: ${firstCity.name} (${firstCity.slug})`);

        const placesResponse = await axios.get(`${API_BASE_URL}/cities/${firstCity.slug}/places`);
        console.log(`Places response status:`, placesResponse.status);
        console.log(`Places response structure:`, {
          success: placesResponse.data.success,
          hasData: !!placesResponse.data.data,
          dataKeys: placesResponse.data.data ? Object.keys(placesResponse.data.data) : 'No data',
          placesCount: placesResponse.data.data?.places?.length || 0
        });

        if (placesResponse.data.data?.places?.length > 0) {
          const firstPlace = placesResponse.data.data.places[0];
          console.log(`Sample place:`, {
            name: firstPlace.name,
            category: firstPlace.category,
            hasTimings: !!firstPlace.timings,
            hasOpeningHours: !!firstPlace.timings?.openingHours
          });
        }
      } else {
        console.log('No cities found to test places endpoint');
      }
    } catch (placesError) {
      console.log(`Places endpoint error:`, placesError.response?.status || placesError.message);
      if (placesError.response?.data) {
        console.log(`Error details:`, placesError.response.data);
      }
    }

  } catch (error) {
    console.error('API Test Error:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
  }
}

testAPI();
