// Simple test script to verify API responses
const axios = require('axios');

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

async function testAPI() {
  console.log('Testing API endpoints...');
  console.log('API Base URL:', API_BASE_URL);

  try {
    // Test cities endpoint
    console.log('\n1. Testing /cities endpoint...');
    const citiesResponse = await axios.get(`${API_BASE_URL}/cities`);
    console.log('Cities response status:', citiesResponse.status);
    console.log('Cities response structure:', {
      success: citiesResponse.data.success,
      hasData: !!citiesResponse.data.data,
      dataKeys: citiesResponse.data.data ? Object.keys(citiesResponse.data.data) : 'No data',
      itemsCount: citiesResponse.data.data?.items?.length || 'No items array'
    });

    // Test places endpoint
    console.log('\n2. Testing /places endpoint...');
    const placesResponse = await axios.get(`${API_BASE_URL}/places`);
    console.log('Places response status:', placesResponse.status);
    console.log('Places response structure:', {
      success: placesResponse.data.success,
      hasData: !!placesResponse.data.data,
      dataKeys: placesResponse.data.data ? Object.keys(placesResponse.data.data) : 'No data',
      itemsCount: placesResponse.data.data?.items?.length || 'No items array'
    });

    // Test featured cities endpoint
    console.log('\n3. Testing /cities/featured endpoint...');
    const featuredResponse = await axios.get(`${API_BASE_URL}/cities/featured`);
    console.log('Featured cities response status:', featuredResponse.status);
    console.log('Featured cities response structure:', {
      success: featuredResponse.data.success,
      hasData: !!featuredResponse.data.data,
      dataKeys: featuredResponse.data.data ? Object.keys(featuredResponse.data.data) : 'No data',
      itemsCount: featuredResponse.data.data?.items?.length || featuredResponse.data.data?.cities?.length || 'No items/cities array'
    });

  } catch (error) {
    console.error('API Test Error:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
  }
}

testAPI();
