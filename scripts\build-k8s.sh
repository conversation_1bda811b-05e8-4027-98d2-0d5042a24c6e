#!/bin/bash

# Build script for Kubernetes deployment
set -e

echo "🚀 Building HeritEdge for Kubernetes deployment..."

# Build backend
echo "📦 Building backend..."
cd backend
docker build -t heritedge/backend:latest .
cd ..

# Build frontend
echo "📦 Building frontend..."
cd frontend
docker build -t heritedge/frontend:latest .
cd ..

# Build admin with Kubernetes configuration
echo "📦 Building admin for Kubernetes..."
cd admin
docker build \
  --build-arg BUILD_ENV=kubernetes \
  --build-arg NEXT_PUBLIC_API_URL=http://localhost:30001/api \
  --build-arg NEXT_PUBLIC_SERVER_URL=http://localhost:30001 \
  --build-arg NEXT_PUBLIC_UPLOADS_URL=http://localhost:30001/uploads \
  -t heritedge/admin:latest .
cd ..

echo "✅ All images built successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Apply Kubernetes configurations: kubectl apply -f k8s/"
echo "2. Check pod status: kubectl get pods -n heritedge"
echo "3. Access admin at: http://localhost:30002"
echo "4. Access frontend at: http://localhost:30000"
echo "5. Backend API at: http://localhost:30001"
