'use client';

import { useState, useEffect } from 'react';
import { Place, City } from '@/types';
import Modal from '@/components/ui/Modal';
import { adminApiClient } from '@/lib/api';
import toast from 'react-hot-toast';

interface PlaceFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  place?: Place | null;
}

interface PlaceFormData {
  name: string;
  description: string;
  category: string;
  cityId: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  address: string;
  website: string;
  phone: string;
  priceRange: string;
  amenities: string[];
  tags: string[];
  isPublished: boolean;
  isFeatured: boolean;
}

const initialFormData: PlaceFormData = {
  name: '',
  description: '',
  category: '',
  cityId: '',
  coordinates: {
    latitude: 0,
    longitude: 0
  },
  address: '',
  website: '',
  phone: '',
  priceRange: '',
  amenities: [],
  tags: [],
  isPublished: true,
  isFeatured: false
};

const categories = [
  'attraction',
  'restaurant',
  'hotel',
  'museum',
  'park',
  'shopping',
  'entertainment',
  'religious',
  'historical',
  'cultural'
];

const priceRanges = [
  'Free',
  '$',
  '$$',
  '$$$',
  '$$$$'
];

export default function PlaceFormModal({ isOpen, onClose, onSuccess, place }: PlaceFormModalProps) {
  const [formData, setFormData] = useState<PlaceFormData>(initialFormData);
  const [loading, setLoading] = useState(false);
  const [cities, setCities] = useState<City[]>([]);



  useEffect(() => {
    if (isOpen) {
      fetchCities();
    }
  }, [isOpen]);

  useEffect(() => {
    if (place) {
      setFormData({
        name: place.name,
        description: place.description,
        category: place.category,
        cityId: place.city._id,
        coordinates: place.coordinates,
        address: place.address || '',
        website: place.website || '',
        phone: place.phone || '',
        priceRange: place.priceRange || '',
        amenities: place.amenities || [],
        tags: place.tags || [],
        isPublished: place.isPublished,
        isFeatured: place.isFeatured
      });
    } else {
      setFormData(initialFormData);
    }
  }, [place]);

  const fetchCities = async () => {
    try {
      const response = await adminApiClient.getCities({ limit: 100 });
      if (response.success) {
        setCities(response.data.items.flat());
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof PlaceFormData],
          [child]: type === 'number' ? parseFloat(value) || 0 : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'number' ? parseFloat(value) || 0 : value
      }));
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // const handleAddAmenity = () => {
  //   if (amenityInput.trim() && !formData.amenities.includes(amenityInput.trim())) {
  //     setFormData(prev => ({
  //       ...prev,
  //       amenities: [...prev.amenities, amenityInput.trim()]
  //     }));
  //     setAmenityInput('');
  //   }
  // };

  // const handleRemoveAmenity = (amenity: string) => {
  //   setFormData(prev => ({
  //     ...prev,
  //     amenities: prev.amenities.filter(a => a !== amenity)
  //   }));
  // };

  // const handleAddTag = () => {
  //   if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
  //     setFormData(prev => ({
  //       ...prev,
  //       tags: [...prev.tags, tagInput.trim()]
  //     }));
  //     setTagInput('');
  //   }
  // };

  // const handleRemoveTag = (tag: string) => {
  //   setFormData(prev => ({
  //     ...prev,
  //     tags: prev.tags.filter(t => t !== tag)
  //   }));
  // };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      let response;
      if (place) {
        response = await adminApiClient.updatePlace(place._id, formData);
      } else {
        response = await adminApiClient.createPlace(formData);
      }

      if (response.success) {
        toast.success(`Place ${place ? 'updated' : 'created'} successfully`);
        onSuccess();
        onClose();
      } else {
        throw new Error(response.error || `Failed to ${place ? 'update' : 'create'} place`);
      }
    } catch (error) {
      console.error('Error saving place:', error);
      toast.error(`Failed to ${place ? 'update' : 'create'} place`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={place ? 'Edit Place' : 'Add New Place'}
      size="xl"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Place Name *
            </label>
            <input
              type="text"
              name="name"
              id="name"
              required
              value={formData.name}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700">
              Category *
            </label>
            <select
              name="category"
              id="category"
              required
              value={formData.category}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select Category</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="cityId" className="block text-sm font-medium text-gray-700">
              City *
            </label>
            <select
              name="cityId"
              id="cityId"
              required
              value={formData.cityId}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select City</option>
              {cities.map(city => (
                <option key={city._id} value={city._id}>
                  {city.name}, {city.country}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="priceRange" className="block text-sm font-medium text-gray-700">
              Price Range
            </label>
            <select
              name="priceRange"
              id="priceRange"
              value={formData.priceRange}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select Price Range</option>
              {priceRanges.map(range => (
                <option key={range} value={range}>
                  {range}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700">
            Description *
          </label>
          <textarea
            name="description"
            id="description"
            rows={4}
            required
            value={formData.description}
            onChange={handleInputChange}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Status */}
        <div className="flex items-center space-x-6">
          <div className="flex items-center">
            <input
              id="isPublished"
              name="isPublished"
              type="checkbox"
              checked={formData.isPublished}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
              Published
            </label>
          </div>

          <div className="flex items-center">
            <input
              id="isFeatured"
              name="isFeatured"
              type="checkbox"
              checked={formData.isFeatured}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isFeatured" className="ml-2 block text-sm text-gray-900">
              Featured
            </label>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Saving...' : place ? 'Update Place' : 'Create Place'}
          </button>
        </div>
      </form>
    </Modal>
  );
}
