version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: heritedge-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: heritedge
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js
    networks:
      - heritedge-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    image: heritedge/backend:latest
    container_name: heritedge-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5001
      MONGODB_URI: ********************************************************************
      JWT_SECRET: your_super_secret_jwt_key_here_change_in_production
      JWT_REFRESH_SECRET: your_super_secret_refresh_key_here_change_in_production
      JWT_EXPIRE: 24h
      JWT_REFRESH_EXPIRE: 7d
      FRONTEND_URL: http://localhost:3000
      ADMIN_URL: http://localhost:3001
    ports:
      - "5001:5001"
    volumes:
      - backend_uploads:/app/uploads
    depends_on:
      - mongodb
    networks:
      - heritedge-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:5001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Main Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    image: heritedge/frontend:latest
    container_name: heritedge-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: http://localhost:5001/api
      NEXT_PUBLIC_FRONTEND_URL: http://localhost:3000
      NEXTAUTH_URL: http://localhost:3000
      NEXTAUTH_SECRET: your_nextauth_secret_here_change_in_production
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - heritedge-network

  # Admin Frontend
  admin:
    build:
      context: ./admin
      dockerfile: Dockerfile
    image: heritedge/admin:latest
    container_name: heritedge-admin
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: http://localhost:5001/api
      NEXT_PUBLIC_APP_NAME: HeritEdge Admin Console
      NEXT_PUBLIC_APP_VERSION: 1.0.0
    ports:
      - "3001:3000"
    depends_on:
      - backend
    networks:
      - heritedge-network

volumes:
  mongodb_data:
    driver: local
  backend_uploads:
    driver: local

networks:
  heritedge-network:
    driver: bridge
