const mongoose = require('mongoose');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/heritedge';

async function setupLondon() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully');

    // Get collections
    const citiesCollection = mongoose.connection.db.collection('cities');
    const placesCollection = mongoose.connection.db.collection('places');

    // Check if London city exists
    let londonCity = await citiesCollection.findOne({ slug: 'london' });
    
    if (!londonCity) {
      console.log('🏙️ London city not found. Creating London city...');
      
      // Create London city data
      const londonCityData = {
        name: 'London',
        slug: 'london',
        country: 'United Kingdom',
        continent: 'Europe',
        description: 'The capital and largest city of England and the United Kingdom, known for its rich history, iconic landmarks, and vibrant culture.',
        images: [
          {
            url: 'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=1200&q=80',
            alt: 'London skyline with Big Ben',
            caption: 'London skyline featuring Big Ben and the Thames',
            isPrimary: true
          }
        ],
        coordinates: {
          latitude: 51.5074,
          longitude: -0.1278
        },
        timezone: 'Europe/London',
        currency: 'GBP',
        language: 'English',
        population: 9648110,
        area: 1572,
        elevation: 35,
        climate: {
          type: 'Temperate oceanic',
          averageTemperature: 11,
          rainyDays: 164,
          sunnyDays: 201
        },
        bestTimeToVisit: {
          months: ['May', 'June', 'July', 'August', 'September'],
          reason: 'Mild weather and longer daylight hours'
        },
        transportation: {
          airport: 'Heathrow Airport (LHR)',
          publicTransport: ['Underground', 'Bus', 'Overground', 'River services'],
          walkability: 'High'
        },
        costOfLiving: {
          budget: 80,
          midRange: 150,
          luxury: 300,
          currency: 'GBP'
        },
        attractions: ['Big Ben', 'Tower of London', 'British Museum', 'Westminster Abbey'],
        cuisine: ['Fish and Chips', 'Sunday Roast', 'Afternoon Tea', 'Indian Curry'],
        culture: {
          heritage: 'Rich royal and parliamentary history',
          arts: 'World-class museums, theaters, and galleries',
          festivals: ['Notting Hill Carnival', 'London Film Festival', 'Pride in London']
        },
        safety: {
          overall: 'High',
          touristAreas: 'Very Safe',
          nightlife: 'Generally Safe'
        },
        isPopular: true,
        isFeatured: true,
        popularityScore: 95,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Insert London city
      const cityResult = await citiesCollection.insertOne(londonCityData);
      londonCity = { ...londonCityData, _id: cityResult.insertedId };
      console.log(`✅ Created London city with ID: ${londonCity._id}`);
    } else {
      console.log(`✅ Found existing London city: ${londonCity.name} (ID: ${londonCity._id})`);
    }

    // Load London places data
    const placesData = require('./london-places.json');
    
    // Clear existing places for London
    await placesCollection.deleteMany({ city: londonCity._id });
    console.log('🗑️ Cleared existing places for London');

    // Update places data with correct city ObjectId
    const updatedPlaces = placesData.map(place => ({
      ...place,
      city: londonCity._id,
      createdAt: new Date(),
      updatedAt: new Date()
    }));

    // Insert places
    const result = await placesCollection.insertMany(updatedPlaces);
    console.log(`✅ Successfully imported ${result.insertedCount} places for London`);

    // Verify and display results
    const totalPlaces = await placesCollection.countDocuments({ city: londonCity._id });
    console.log(`📊 Total places for London: ${totalPlaces}`);

    // Group by category
    const placesByCategory = await placesCollection.aggregate([
      { $match: { city: londonCity._id } },
      { $group: { _id: '$category', count: { $sum: 1 }, places: { $push: '$name' } } },
      { $sort: { _id: 1 } }
    ]).toArray();

    console.log('\n📍 London places by category:');
    placesByCategory.forEach(category => {
      console.log(`\n🏷️  ${category._id.toUpperCase()} (${category.count} places):`);
      category.places.forEach(placeName => {
        console.log(`   - ${placeName}`);
      });
    });

    console.log('\n🎉 London setup completed successfully!');
    console.log('🌐 You can now view London at: http://localhost:3001/cities/london');
    console.log('📱 Click the "Places" tab to see the categorized places in a 2x2 grid layout');

  } catch (error) {
    console.error('❌ Failed to setup London:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  setupLondon();
}

module.exports = { setupLondon };
