apiVersion: v1
kind: Secret
metadata:
  name: heritedge-secrets
  namespace: heritedge
type: Opaque
data:
  # Base64 encoded secrets
  # JWT_SECRET: your_super_secret_jwt_key_here_change_in_production
  JWT_SECRET: eW91cl9zdXBlcl9zZWNyZXRfand0X2tleV9oZXJlX2NoYW5nZV9pbl9wcm9kdWN0aW9u
  # JWT_REFRESH_SECRET: your_super_secret_refresh_key_here_change_in_production  
  JWT_REFRESH_SECRET: eW91cl9zdXBlcl9zZWNyZXRfcmVmcmVzaF9rZXlfaGVyZV9jaGFuZ2VfaW5fcHJvZHVjdGlvbg==
  # NEXTAUTH_SECRET: your_nextauth_secret_here_change_in_production
  NEXTAUTH_SECRET: eW91cl9uZXh0YXV0aF9zZWNyZXRfaGVyZV9jaGFuZ2VfaW5fcHJvZHVjdGlvbg==
  # MONGO_ROOT_PASSWORD: password123
  MONGO_ROOT_PASSWORD: cGFzc3dvcmQxMjM=
  # MONGO_ROOT_USERNAME: admin
  MONGO_ROOT_USERNAME: YWRtaW4=

---
apiVersion: v1
kind: Secret
metadata:
  name: mongodb-secret
  namespace: heritedge
type: Opaque
data:
  # MongoDB credentials
  # username: admin
  username: YWRtaW4=
  # password: password123
  password: cGFzc3dvcmQxMjM=
