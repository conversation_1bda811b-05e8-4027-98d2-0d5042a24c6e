const fs = require('fs');

// Function to fix a file
function fixFile(filePath, replacements) {
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  replacements.forEach(({ from, to }) => {
    if (content.includes(from)) {
      content = content.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
      modified = true;
      console.log(`Fixed in ${filePath}: ${from} -> ${to}`);
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated: ${filePath}`);
  }
}

// Fix all remaining lint errors
const fixes = [
  // Media page - remove remaining unused imports and variables
  {
    file: 'src/app/media/page.tsx',
    replacements: [
      { from: '  FunnelIcon,', to: '' },
      { from: '  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");', to: '' }
    ]
  },
  
  // Places page - fix any type and add useCallback
  {
    file: 'src/app/places/page.tsx',
    replacements: [
      { from: 'import { useState, useEffect } from \'react\';', to: 'import { useState, useEffect, useCallback } from \'react\';' },
      { from: 'error: any', to: 'error: Error | string' }
    ]
  },
  
  // Reviews page - fix any type and add useCallback
  {
    file: 'src/app/reviews/page.tsx',
    replacements: [
      { from: 'import { useState, useEffect } from \'react\';', to: 'import { useState, useEffect, useCallback } from \'react\';' },
      { from: 'error: any', to: 'error: Error | string' }
    ]
  },
  
  // Users page - fix any type and add useCallback
  {
    file: 'src/app/users/page.tsx',
    replacements: [
      { from: 'import { useState, useEffect } from \'react\';', to: 'import { useState, useEffect, useCallback } from \'react\';' },
      { from: 'error: any', to: 'error: Error | string' }
    ]
  },
  
  // PlaceFormModal - remove unused variables
  {
    file: 'src/components/forms/PlaceFormModal.tsx',
    replacements: [
      { from: '  const [amenityInput, setAmenityInput] = useState(\'\');', to: '' },
      { from: '  const [tagInput, setTagInput] = useState(\'\');', to: '' }
    ]
  },
  
  // AuthContext - fix unused error variable
  {
    file: 'src/contexts/AuthContext.tsx',
    replacements: [
      { from: '        } catch (error) {', to: '        } catch {' }
    ]
  }
];

console.log('Fixing final admin lint errors...');
fixes.forEach(({ file, replacements }) => {
  fixFile(file, replacements);
});

console.log('Done!');
