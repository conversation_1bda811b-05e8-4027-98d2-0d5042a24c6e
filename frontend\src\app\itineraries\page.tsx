'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  CalendarDaysIcon,
  MapPinIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  HeartIcon,
  ClockIcon,
  GlobeAltIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline';
import {
  CalendarDaysIcon as CalendarIconSolid,
  HeartIcon as HeartIconSolid
} from '@heroicons/react/24/solid';
import { apiClient } from '@/lib/api';
import { Breadcrumb } from '@/components/ui/Breadcrumb';
import toast from 'react-hot-toast';

interface Itinerary {
  _id: string;
  title: string;
  description?: string;
  city: {
    _id: string;
    name: string;
    slug: string;
    country: string;
  };
  duration: number;
  travelStyle: string;
  status: 'draft' | 'published' | 'archived';
  isPublic: boolean;
  likes: string[];
  views: number;
  createdAt: string;
  updatedAt: string;
}

export default function ItinerariesPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();

  const [itineraries, setItineraries] = useState<Itinerary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'draft' | 'published' | 'archived'>('all');

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch user's itineraries
  useEffect(() => {
    const fetchItineraries = async () => {
      console.log('=== ITINERARIES DEBUG ===');
      console.log('isAuthenticated:', isAuthenticated);
      console.log('authLoading:', authLoading);
      console.log('user object:', user);
      console.log('user.id:', user?.id);
      console.log('========================');

      if (!isAuthenticated || !user) {
        console.log('Not authenticated or no user:', { isAuthenticated, user });
        return;
      }

      if (!user.id) {
        console.error('User ID is missing:', user);
        setError('User ID is missing. Please try logging in again.');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        console.log('Fetching itineraries for user:', user.id);
        console.log('API URL will be:', `/itineraries/user/${user.id}`);

        const response = await apiClient.getUserItineraries(user.id);
        console.log('Itineraries API response:', response);

        if (response.success && response.data) {
          console.log('API Response Data:', response.data);
          console.log('Itineraries array:', response.data.itineraries);
          console.log('Itineraries count:', response.data.itineraries?.length || 0);
          setItineraries(response.data.itineraries || []);
        } else {
          console.log('API response not successful:', response);
          setItineraries([]);
        }
      } catch (err: any) {
        console.error('Itineraries error:', err);
        console.error('Error details:', {
          status: err.response?.status,
          data: err.response?.data,
          message: err.message,
          url: err.config?.url
        });

        // Handle specific error cases
        if (err.response?.status === 401) {
          setError('Please log in to view your itineraries');
        } else if (err.response?.status === 404) {
          setError('User not found. Please try logging in again.');
        } else {
          setError(err.response?.data?.error || err.message || 'Failed to load itineraries');
        }
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated && user) {
      fetchItineraries();
    }
  }, [isAuthenticated, user]);

  const handleDeleteItinerary = async (itineraryId: string) => {
    if (!confirm('Are you sure you want to delete this itinerary? This action cannot be undone.')) {
      return;
    }

    try {
      await apiClient.deleteItinerary(itineraryId);
      setItineraries(prev => prev.filter(item => item._id !== itineraryId));
      toast.success('Itinerary deleted successfully');
    } catch (error: any) {
      console.error('Delete itinerary error:', error);
      toast.error('Failed to delete itinerary');
    }
  };



  const filteredItineraries = itineraries.filter(itinerary => {
    if (filter === 'all') return true;
    return itinerary.status === filter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'archived':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };



  const getTravelStyleIcon = (style: string) => {
    switch (style) {
      case 'solo':
        return '🧳';
      case 'couple':
        return '💑';
      case 'family':
        return '👨‍👩‍👧‍👦';
      case 'group':
        return '👥';
      case 'business':
        return '💼';
      default:
        return '🧳';
    }
  };

  // Add test itinerary function for debugging
  const createTestItinerary = async () => {
    if (!user?.id) {
      console.error('No user ID available');
      return;
    }

    try {
      const testData = {
        title: "Test Itinerary - " + new Date().toLocaleDateString(),
        description: "A test itinerary created for debugging",
        cityId: "507f1f77bcf86cd799439011", // Sample city ID
        duration: 3,
        startDate: new Date().toISOString(),
        budget: "mid-range",
        travelStyle: "solo",
        interests: ["historical", "cultural"],
        isPublic: false,
        status: "draft"
      };

      console.log('Creating test itinerary:', testData);
      const response = await apiClient.createItinerary(testData);
      console.log('Test itinerary created:', response);

      if (response.success) {
        // Refresh the itineraries list
        window.location.reload();
      }
    } catch (error) {
      console.error('Failed to create test itinerary:', error);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <CalendarDaysIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Itineraries</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">My Itineraries</h1>
              <p className="text-xl text-blue-100 max-w-2xl">
                Plan, organize, and manage your travel adventures with detailed day-by-day itineraries
              </p>
            </div>
            <Link
              href="/itineraries/new"
              className="hidden md:flex items-center space-x-2 px-6 py-3 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors font-medium"
            >
              <PlusIcon className="h-5 w-5" />
              <span>Create Itinerary</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <Breadcrumb items={[{ label: 'Itineraries', isActive: true }]} />
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters and Create Button */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div className="flex space-x-1 bg-white rounded-lg p-1 border border-gray-200">
            {['all', 'draft', 'published', 'archived'].map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status as any)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors capitalize ${
                  filter === status
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                {status} ({status === 'all' ? itineraries.length : itineraries.filter(i => i.status === status).length})
              </button>
            ))}
          </div>

          <Link
            href="/itineraries/new"
            className="md:hidden flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Create Itinerary</span>
          </Link>
        </div>

        {/* Itineraries Grid */}
        {filteredItineraries.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredItineraries.map((itinerary) => (
              <div key={itinerary._id} className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden group">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                        <Link href={`/itineraries/${itinerary._id}`}>
                          {itinerary.title}
                        </Link>
                      </h3>
                      <div className="flex items-center text-gray-600 mb-2">
                        <MapPinIcon className="h-4 w-4 mr-1" />
                        <span className="text-sm">{itinerary.city.name}, {itinerary.city.country}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(itinerary.status)}`}>
                        {itinerary.status}
                      </span>
                      {!itinerary.isPublic && (
                        <LockClosedIcon className="h-4 w-4 text-gray-400" title="Private" />
                      )}
                    </div>
                  </div>

                  {itinerary.description && (
                    <p className="text-gray-600 text-sm line-clamp-2 mb-4">
                      {itinerary.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <ClockIcon className="h-4 w-4 mr-1" />
                        <span>{itinerary.duration} days</span>
                      </div>
                      <div className="flex items-center">
                        <span className="mr-1">{getTravelStyleIcon(itinerary.travelStyle)}</span>
                        <span className="capitalize">{itinerary.travelStyle}</span>
                      </div>
                    </div>
                  </div>

                  {itinerary.isPublic && (
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <HeartIconSolid className="h-4 w-4 mr-1 text-red-500" />
                          <span>{itinerary.likes.length}</span>
                        </div>
                        <div className="flex items-center">
                          <EyeIcon className="h-4 w-4 mr-1" />
                          <span>{itinerary.views}</span>
                        </div>
                      </div>
                      <GlobeAltIcon className="h-4 w-4 text-green-500" title="Public" />
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <span className="text-xs text-gray-500">
                      Updated {new Date(itinerary.updatedAt).toLocaleDateString()}
                    </span>
                    <div className="flex items-center space-x-2">
                      <Link
                        href={`/itineraries/${itinerary._id}`}
                        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="View itinerary"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </Link>
                      <Link
                        href={`/itineraries/${itinerary._id}/edit`}
                        className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                        title="Edit itinerary"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </Link>
                      <button
                        onClick={() => handleDeleteItinerary(itinerary._id)}
                        className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Delete itinerary"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <CalendarIconSolid className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {filter === 'all' ? 'No itineraries yet' : `No ${filter} itineraries`}
            </h3>
            <p className="text-gray-600 mb-6">
              {filter === 'all'
                ? 'Create your first itinerary to start planning your cultural journey.'
                : `You don't have any ${filter} itineraries yet.`
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <Link
                href="/itineraries/new"
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <PlusIcon className="h-5 w-5 mr-2" />
                Create Your First Itinerary
              </Link>
              <button
                onClick={createTestItinerary}
                className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
              >
                🧪 Create Test Itinerary (Debug)
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
