'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import AdminLayout from '@/components/layout/AdminLayout';
import { City, CityFilters } from '@/types';
import { adminApiClient } from '@/lib/api';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  EllipsisVerticalIcon,
  BuildingOfficeIcon,
  MapPinIcon,
  StarIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import toast from 'react-hot-toast';
import CityFormModal from '@/components/forms/CityFormModal';

export default function CitiesPage() {
  const { admin } = useAuth();
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCityModal, setShowCityModal] = useState(false);
  const [selectedCity, setSelectedCity] = useState<City | null>(null);
  const [filters, setFilters] = useState<CityFilters>({
    page: 1,
    limit: 10,
    search: '',
    country: '',
    isPublished: undefined,
    isFeatured: undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

 

  const fetchCities = useCallback(async () => {
    try {
      setLoading(true);

      const response = await adminApiClient.getCities(filters);

      if (response.success) {
        setCities(response.data.items.flat());
        setPagination(response.data.pagination);
      } else {
        throw new Error(response.error || 'Failed to fetch cities');
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
      toast.error('Failed to fetch cities');

      // Fallback to mock data if API fails
      const mockCities: City[] = [
        {
          _id: '1',
          name: 'Paris',
          slug: 'paris',
          country: 'France',
          description: 'The City of Light, known for its art, fashion, and culture.',
          coordinates: { latitude: 48.8566, longitude: 2.3522 },
          images: [
            { url: '/images/cities/paris.jpg', alt: 'Paris skyline', isPrimary: true }
          ],
          averageRating: 4.5,
          totalReviews: 1250,
          isPublished: true,
          isFeatured: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-15T10:30:00Z'
        },
        {
          _id: '2',
          name: 'Tokyo',
          slug: 'tokyo',
          country: 'Japan',
          description: 'A bustling metropolis blending traditional and modern culture.',
          coordinates: { latitude: 35.6762, longitude: 139.6503 },
          images: [
            { url: '/images/cities/tokyo.jpg', alt: 'Tokyo skyline', isPrimary: true }
          ],
          averageRating: 4.7,
          totalReviews: 890,
          isPublished: true,
          isFeatured: false,
          createdAt: '2023-12-15T00:00:00Z',
          updatedAt: '2024-01-10T15:45:00Z'
        },
        {
          _id: '3',
          name: 'New York',
          slug: 'new-york',
          country: 'United States',
          description: 'The Big Apple, a global hub for finance, arts, and culture.',
          coordinates: { latitude: 40.7128, longitude: -74.0060 },
          images: [
            { url: '/images/cities/new-york.jpg', alt: 'New York skyline', isPrimary: true }
          ],
          averageRating: 4.3,
          totalReviews: 2100,
          isPublished: false,
          isFeatured: false,
          createdAt: '2023-11-20T00:00:00Z',
          updatedAt: '2024-01-05T08:20:00Z'
        }
      ];

      setCities(mockCities);
      setPagination({
        page: 1,
        limit: 10,
        total: mockCities.length,
        pages: 1
      });
    } finally {
      setLoading(false);
    }
  }, [filters]);

   useEffect(() => {
    fetchCities();
  }, [fetchCities]);
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({
      ...prev,
      search: e.target.value,
      page: 1
    }));
  };

  const handleFilterChange = (key: keyof CityFilters, value: string | number | boolean | undefined) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1
    }));
  };

  const handleToggleStatus = async (cityId: string) => {
    try {
      const response = await adminApiClient.toggleCityStatus(cityId);

      if (response.success) {
        setCities(prev => prev.map(city =>
          city._id === cityId
            ? { ...city, isPublished: !city.isPublished }
            : city
        ));
        toast.success('City status updated successfully');
      } else {
        throw new Error(response.error || 'Failed to update city status');
      }
    } catch (error) {
      console.error('Error toggling city status:', error);
      toast.error('Failed to update city status');
    }
  };

  const handleToggleFeatured = async (cityId: string) => {
    try {
      const response = await adminApiClient.toggleCityFeatured(cityId);

      if (response.success) {
        setCities(prev => prev.map(city =>
          city._id === cityId
            ? { ...city, isFeatured: !city.isFeatured }
            : city
        ));
        toast.success('City featured status updated successfully');
      } else {
        throw new Error(response.error || 'Failed to update city featured status');
      }
    } catch (error) {
      console.error('Error toggling city featured status:', error);
      toast.error('Failed to update city featured status');
    }
  };

  const handleAddCity = () => {
    setSelectedCity(null);
    setShowCityModal(true);
  };

  const handleEditCity = (city: City) => {
    setSelectedCity(city);
    setShowCityModal(true);
  };

  const handleCloseModal = () => {
    setShowCityModal(false);
    setSelectedCity(null);
  };

  const handleModalSuccess = () => {
    fetchCities();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Cities</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage cities and their information
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              type="button"
              onClick={handleAddCity}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Add City
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white shadow rounded-lg">
          <div className="p-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
              {/* Search */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search cities..."
                  value={filters.search}
                  onChange={handleSearch}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Country Filter */}
              <input
                type="text"
                placeholder="Filter by country..."
                value={filters.country}
                onChange={(e) => handleFilterChange('country', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />

              {/* Published Filter */}
              <select
                value={filters.isPublished === undefined ? '' : filters.isPublished.toString()}
                onChange={(e) => handleFilterChange('isPublished', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Status</option>
                <option value="true">Published</option>
                <option value="false">Unpublished</option>
              </select>

              {/* Featured Filter */}
              <select
                value={filters.isFeatured === undefined ? '' : filters.isFeatured.toString()}
                onChange={(e) => handleFilterChange('isFeatured', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Featured</option>
                <option value="true">Featured</option>
                <option value="false">Not Featured</option>
              </select>

              {/* Sort */}
              <select
                value={`${filters.sortBy}-${filters.sortOrder}`}
                onChange={(e) => {
                  const [sortBy, sortOrder] = e.target.value.split('-');
                  handleFilterChange('sortBy', sortBy);
                  handleFilterChange('sortOrder', sortOrder);
                }}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="createdAt-desc">Newest First</option>
                <option value="createdAt-asc">Oldest First</option>
                <option value="name-asc">Name A-Z</option>
                <option value="name-desc">Name Z-A</option>
                <option value="averageRating-desc">Highest Rated</option>
              </select>
            </div>
          </div>
        </div>

        {/* Cities Grid */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">Loading cities...</p>
            </div>
          ) : cities.length === 0 ? (
            <div className="p-8 text-center">
              <BuildingOfficeIcon className="h-12 w-12 text-gray-400 mx-auto" />
              <p className="mt-2 text-sm text-gray-500">No cities found</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 p-6">
              {cities.map((city) => (
                <div key={city._id} className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  {/* City Image */}
                  <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-lg overflow-hidden">
                    {city.images && city.images.length > 0 ? (
                      <img
                        src={city.images[0].url}
                        alt={city.images[0].alt}
                        className="w-full h-48 object-cover"
                      />
                    ) : (
                      <div className="w-full h-48 bg-gray-300 flex items-center justify-center">
                        <BuildingOfficeIcon className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* City Info */}
                  <div className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900">{city.name}</h3>
                        <p className="text-sm text-gray-600 flex items-center mt-1">
                          <MapPinIcon className="h-4 w-4 mr-1" />
                          {city.country}
                        </p>
                      </div>
                      <Menu as="div" className="relative">
                        <Menu.Button className="flex items-center text-gray-400 hover:text-gray-600">
                          <EllipsisVerticalIcon className="h-5 w-5" />
                        </Menu.Button>
                        <Transition
                          as={Fragment}
                          enter="transition ease-out duration-100"
                          enterFrom="transform opacity-0 scale-95"
                          enterTo="transform opacity-100 scale-100"
                          leave="transition ease-in duration-75"
                          leaveFrom="transform opacity-100 scale-100"
                          leaveTo="transform opacity-0 scale-95"
                        >
                          <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                            <div className="py-1">
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    className={`${
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                                    } block w-full px-4 py-2 text-left text-sm`}
                                  >
                                    View Details
                                  </button>
                                )}
                              </Menu.Item>
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => handleEditCity(city)}
                                    className={`${
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                                    } block w-full px-4 py-2 text-left text-sm`}
                                  >
                                    Edit City
                                  </button>
                                )}
                              </Menu.Item>
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => handleToggleStatus(city._id)}
                                    className={`${
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                                    } block w-full px-4 py-2 text-left text-sm`}
                                  >
                                    {city.isPublished ? 'Unpublish' : 'Publish'}
                                  </button>
                                )}
                              </Menu.Item>
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => handleToggleFeatured(city._id)}
                                    className={`${
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                                    } block w-full px-4 py-2 text-left text-sm`}
                                  >
                                    {city.isFeatured ? 'Unfeature' : 'Feature'}
                                  </button>
                                )}
                              </Menu.Item>
                              {admin?.role === 'super_admin' && (
                                <Menu.Item>
                                  {({ active }) => (
                                    <button
                                      className={`${
                                        active ? 'bg-gray-100 text-red-900' : 'text-red-700'
                                      } block w-full px-4 py-2 text-left text-sm`}
                                    >
                                      Delete City
                                    </button>
                                  )}
                                </Menu.Item>
                              )}
                            </div>
                          </Menu.Items>
                        </Transition>
                      </Menu>
                    </div>

                    <p className="text-sm text-gray-600 mt-2 line-clamp-2">{city.description}</p>

                    {/* Stats */}
                    <div className="flex items-center justify-between mt-4">
                      <div className="flex items-center text-sm text-gray-500">
                        <StarIcon className="h-4 w-4 text-yellow-400 mr-1" />
                        {city.averageRating.toFixed(1)} ({city.totalReviews} reviews)
                      </div>
                      <div className="flex items-center space-x-2">
                        {city.isPublished ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <CheckCircleIcon className="h-3 w-3 mr-1" />
                            Published
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <XCircleIcon className="h-3 w-3 mr-1" />
                            Unpublished
                          </span>
                        )}
                        {city.isFeatured && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <StarIcon className="h-3 w-3 mr-1" />
                            Featured
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="text-xs text-gray-400 mt-2">
                      Created: {formatDate(city.createdAt)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  disabled={pagination.page === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  disabled={pagination.page === pagination.pages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{((pagination.page - 1) * pagination.limit) + 1}</span> to{' '}
                    <span className="font-medium">
                      {Math.min(pagination.page * pagination.limit, pagination.total)}
                    </span>{' '}
                    of <span className="font-medium">{pagination.total}</span> results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    <button
                      disabled={pagination.page === 1}
                      onClick={() => handleFilterChange('page', pagination.page - 1)}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <button
                      disabled={pagination.page === pagination.pages}
                      onClick={() => handleFilterChange('page', pagination.page + 1)}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* City Form Modal */}
        <CityFormModal
          isOpen={showCityModal}
          onClose={handleCloseModal}
          onSuccess={handleModalSuccess}
          city={selectedCity}
        />
      </div>
    </AdminLayout>
  );
}
