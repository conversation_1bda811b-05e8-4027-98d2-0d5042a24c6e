#!/bin/bash

# HeritEdge Kubernetes Deployment Script
echo "🚀 Deploying HeritEdge to Kubernetes..."

# Set error handling
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed. Please install kubectl and try again."
    exit 1
fi

# Check if Kubernetes cluster is accessible
if ! kubectl cluster-info > /dev/null 2>&1; then
    print_error "Cannot connect to Kubernetes cluster. Please check your cluster connection."
    exit 1
fi

print_status "Kubernetes cluster is accessible ✓"

# Create namespace
print_status "Creating namespace..."
kubectl apply -f k8s/namespace.yaml

# Apply ConfigMaps and Secrets
print_status "Applying ConfigMaps and Secrets..."
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml

# Apply Persistent Volumes
print_status "Creating Persistent Volumes..."
kubectl apply -f k8s/persistent-volumes.yaml

# Wait for PVCs to be bound
print_status "Waiting for Persistent Volume Claims to be bound..."
kubectl wait --for=condition=Bound pvc/mongodb-pvc -n heritedge --timeout=60s
kubectl wait --for=condition=Bound pvc/backend-uploads-pvc -n heritedge --timeout=60s

# Deploy MongoDB
print_status "Deploying MongoDB..."
kubectl apply -f k8s/mongodb-deployment.yaml

# Wait for MongoDB to be ready
print_status "Waiting for MongoDB to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/mongodb-deployment -n heritedge

# Deploy Backend
print_status "Deploying Backend..."
kubectl apply -f k8s/backend-deployment.yaml

# Wait for Backend to be ready
print_status "Waiting for Backend to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/heritedge-backend-deployment -n heritedge

# Deploy Frontend
print_status "Deploying Frontend..."
kubectl apply -f k8s/frontend-deployment.yaml

# Wait for Frontend to be ready
print_status "Waiting for Frontend to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/heritedge-frontend-deployment -n heritedge

# Deploy Admin
print_status "Deploying Admin..."
kubectl apply -f k8s/admin-deployment.yaml

# Wait for Admin to be ready
print_status "Waiting for Admin to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/heritedge-admin-deployment -n heritedge

# Get service information
print_success "Deployment completed successfully! 🎉"
print_status "Getting service information..."

echo ""
print_status "📋 Service Status:"
kubectl get pods -n heritedge
echo ""
kubectl get services -n heritedge
echo ""

# Get NodePort information
FRONTEND_NODEPORT=$(kubectl get service heritedge-frontend-nodeport -n heritedge -o jsonpath='{.spec.ports[0].nodePort}')
ADMIN_NODEPORT=$(kubectl get service heritedge-admin-nodeport -n heritedge -o jsonpath='{.spec.ports[0].nodePort}')
BACKEND_NODEPORT=$(kubectl get service heritedge-backend-nodeport -n heritedge -o jsonpath='{.spec.ports[0].nodePort}')

print_success "🌐 Access URLs:"
echo "Frontend: http://localhost:$FRONTEND_NODEPORT"
echo "Admin: http://localhost:$ADMIN_NODEPORT"
echo "Backend API: http://localhost:$BACKEND_NODEPORT"
echo ""

print_success "🔐 Default Admin Credentials:"
echo "Email: <EMAIL>"
echo "Password: admin123456"
echo ""

print_warning "⚠️  Important Security Notes:"
echo "1. Change default admin password after first login"
echo "2. Update JWT secrets in production"
echo "3. Use proper TLS certificates for production deployment"
echo ""

print_status "📊 To monitor the deployment:"
echo "kubectl get pods -n heritedge -w"
echo "kubectl logs -f deployment/heritedge-backend-deployment -n heritedge"
echo ""

print_status "🗑️  To clean up the deployment:"
echo "./scripts/cleanup-k8s.sh"
