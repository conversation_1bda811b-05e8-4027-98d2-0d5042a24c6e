'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import AdminLayout from '@/components/layout/AdminLayout';
import { Place, PlaceFilters } from '@/types';
import { adminApiClient } from '@/lib/api';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  EllipsisVerticalIcon,
  MapPinIcon,
  BuildingOfficeIcon,
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import toast from 'react-hot-toast';

const PLACE_CATEGORIES = [
  'attraction',
  'museum',
  'restaurant',
  'hotel',
  'shopping',
  'entertainment',
  'religious',
  'park',
  'beach',
  'historical'
];

export default function PlacesPage() {
  const { admin } = useAuth();
  const [places, setPlaces] = useState<Place[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<PlaceFilters>({
    page: 1,
    limit: 10,
    search: '',
    category: '',
    city: '',
    isPublished: undefined,
    isFeatured: undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  useEffect(() => {
    fetchPlaces();
  }, [filters]);

  const fetchPlaces = async () => {
    try {
      setLoading(true);

      const response = await adminApiClient.getPlaces(filters);

      if (response.success) {
        setPlaces(response.data.items);
        setPagination(response.data.pagination);
      } else {
        throw new Error(response.error || 'Failed to fetch places');
      }
    } catch (error) {
      console.error('Error fetching places:', error);
      toast.error('Failed to fetch places');

      // Fallback to mock data if API fails
      const mockPlaces: Place[] = [
        {
          _id: '1',
          name: 'Eiffel Tower',
          slug: 'eiffel-tower',
          description: 'Iconic iron lattice tower and symbol of Paris.',
          category: 'attraction',
          city: {
            _id: 'city1',
            name: 'Paris',
            country: 'France'
          },
          coordinates: { latitude: 48.8584, longitude: 2.2945 },
          address: 'Champ de Mars, 5 Avenue Anatole France, 75007 Paris, France',
          website: 'https://www.toureiffel.paris',
          phone: '+33 8 92 70 12 39',
          openingHours: {
            'Monday': '9:30 AM - 11:45 PM',
            'Tuesday': '9:30 AM - 11:45 PM',
            'Wednesday': '9:30 AM - 11:45 PM',
            'Thursday': '9:30 AM - 11:45 PM',
            'Friday': '9:30 AM - 11:45 PM',
            'Saturday': '9:30 AM - 11:45 PM',
            'Sunday': '9:30 AM - 11:45 PM'
          },
          priceRange: '€€€',
          images: [
            { url: '/images/places/eiffel-tower.jpg', alt: 'Eiffel Tower', isPrimary: true }
          ],
          amenities: ['Elevator', 'Restaurant', 'Gift Shop', 'Guided Tours'],
          tags: ['iconic', 'tower', 'views', 'romantic'],
          averageRating: 4.5,
          totalReviews: 15420,
          isPublished: true,
          isFeatured: true,
          popularityScore: 95,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-15T10:30:00Z'
        },
        {
          _id: '2',
          name: 'Louvre Museum',
          slug: 'louvre-museum',
          description: 'World\'s largest art museum and historic monument.',
          category: 'museum',
          city: {
            _id: 'city1',
            name: 'Paris',
            country: 'France'
          },
          coordinates: { latitude: 48.8606, longitude: 2.3376 },
          address: 'Rue de Rivoli, 75001 Paris, France',
          website: 'https://www.louvre.fr',
          phone: '+33 1 40 20 50 50',
          openingHours: {
            'Monday': 'Closed',
            'Tuesday': '9:00 AM - 6:00 PM',
            'Wednesday': '9:00 AM - 9:45 PM',
            'Thursday': '9:00 AM - 6:00 PM',
            'Friday': '9:00 AM - 9:45 PM',
            'Saturday': '9:00 AM - 6:00 PM',
            'Sunday': '9:00 AM - 6:00 PM'
          },
          priceRange: '€€',
          images: [
            { url: '/images/places/louvre.jpg', alt: 'Louvre Museum', isPrimary: true }
          ],
          amenities: ['Audio Guide', 'Wheelchair Access', 'Cafe', 'Gift Shop'],
          tags: ['art', 'museum', 'mona lisa', 'culture'],
          averageRating: 4.7,
          totalReviews: 8930,
          isPublished: true,
          isFeatured: true,
          popularityScore: 92,
          createdAt: '2023-12-15T00:00:00Z',
          updatedAt: '2024-01-10T15:45:00Z'
        },
        {
          _id: '3',
          name: 'Senso-ji Temple',
          slug: 'senso-ji-temple',
          description: 'Ancient Buddhist temple in Asakusa, Tokyo.',
          category: 'religious',
          city: {
            _id: 'city2',
            name: 'Tokyo',
            country: 'Japan'
          },
          coordinates: { latitude: 35.7148, longitude: 139.7967 },
          address: '2-3-1 Asakusa, Taito City, Tokyo 111-0032, Japan',
          website: 'https://www.senso-ji.jp',
          openingHours: {
            'Monday': '6:00 AM - 5:00 PM',
            'Tuesday': '6:00 AM - 5:00 PM',
            'Wednesday': '6:00 AM - 5:00 PM',
            'Thursday': '6:00 AM - 5:00 PM',
            'Friday': '6:00 AM - 5:00 PM',
            'Saturday': '6:00 AM - 5:00 PM',
            'Sunday': '6:00 AM - 5:00 PM'
          },
          priceRange: 'Free',
          images: [
            { url: '/images/places/senso-ji.jpg', alt: 'Senso-ji Temple', isPrimary: true }
          ],
          amenities: ['Prayer Area', 'Souvenir Shop', 'Traditional Market'],
          tags: ['temple', 'buddhist', 'traditional', 'spiritual'],
          averageRating: 4.4,
          totalReviews: 5670,
          isPublished: false,
          isFeatured: false,
          popularityScore: 78,
          createdAt: '2023-11-20T00:00:00Z',
          updatedAt: '2024-01-05T08:20:00Z'
        }
      ];

      setPlaces(mockPlaces);
      setPagination({
        page: 1,
        limit: 10,
        total: mockPlaces.length,
        pages: 1
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({
      ...prev,
      search: e.target.value,
      page: 1
    }));
  };

  const handleFilterChange = (key: keyof PlaceFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1
    }));
  };

  const handleToggleStatus = async (placeId: string) => {
    try {
      const response = await adminApiClient.togglePlaceStatus(placeId);

      if (response.success) {
        setPlaces(prev => prev.map(place =>
          place._id === placeId
            ? { ...place, isPublished: !place.isPublished }
            : place
        ));
        toast.success('Place status updated successfully');
      } else {
        throw new Error(response.error || 'Failed to update place status');
      }
    } catch (error) {
      console.error('Error toggling place status:', error);
      toast.error('Failed to update place status');
    }
  };

  const handleToggleFeatured = async (placeId: string) => {
    try {
      const response = await adminApiClient.togglePlaceFeatured(placeId);

      if (response.success) {
        setPlaces(prev => prev.map(place =>
          place._id === placeId
            ? { ...place, isFeatured: !place.isFeatured }
            : place
        ));
        toast.success('Place featured status updated successfully');
      } else {
        throw new Error(response.error || 'Failed to update place featured status');
      }
    } catch (error) {
      console.error('Error toggling place featured status:', error);
      toast.error('Failed to update place featured status');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      attraction: 'bg-blue-100 text-blue-800',
      museum: 'bg-purple-100 text-purple-800',
      restaurant: 'bg-orange-100 text-orange-800',
      hotel: 'bg-green-100 text-green-800',
      shopping: 'bg-pink-100 text-pink-800',
      entertainment: 'bg-yellow-100 text-yellow-800',
      religious: 'bg-indigo-100 text-indigo-800',
      park: 'bg-emerald-100 text-emerald-800',
      beach: 'bg-cyan-100 text-cyan-800',
      historical: 'bg-amber-100 text-amber-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Places</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage places and attractions
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Add Place
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white shadow rounded-lg">
          <div className="p-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-6">
              {/* Search */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search places..."
                  value={filters.search}
                  onChange={handleSearch}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Category Filter */}
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Categories</option>
                {PLACE_CATEGORIES.map(category => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>

              {/* City Filter */}
              <input
                type="text"
                placeholder="Filter by city..."
                value={filters.city}
                onChange={(e) => handleFilterChange('city', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />

              {/* Published Filter */}
              <select
                value={filters.isPublished === undefined ? '' : filters.isPublished.toString()}
                onChange={(e) => handleFilterChange('isPublished', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Status</option>
                <option value="true">Published</option>
                <option value="false">Unpublished</option>
              </select>

              {/* Featured Filter */}
              <select
                value={filters.isFeatured === undefined ? '' : filters.isFeatured.toString()}
                onChange={(e) => handleFilterChange('isFeatured', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Featured</option>
                <option value="true">Featured</option>
                <option value="false">Not Featured</option>
              </select>

              {/* Sort */}
              <select
                value={`${filters.sortBy}-${filters.sortOrder}`}
                onChange={(e) => {
                  const [sortBy, sortOrder] = e.target.value.split('-');
                  handleFilterChange('sortBy', sortBy);
                  handleFilterChange('sortOrder', sortOrder);
                }}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="createdAt-desc">Newest First</option>
                <option value="createdAt-asc">Oldest First</option>
                <option value="name-asc">Name A-Z</option>
                <option value="name-desc">Name Z-A</option>
                <option value="averageRating-desc">Highest Rated</option>
                <option value="popularityScore-desc">Most Popular</option>
              </select>
            </div>
          </div>
        </div>

        {/* Places Grid */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">Loading places...</p>
            </div>
          ) : places.length === 0 ? (
            <div className="p-8 text-center">
              <MapPinIcon className="h-12 w-12 text-gray-400 mx-auto" />
              <p className="mt-2 text-sm text-gray-500">No places found</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 p-6">
              {places.map((place) => (
                <div key={place._id} className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  {/* Place Image */}
                  <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-lg overflow-hidden">
                    {place.images && place.images.length > 0 ? (
                      <img
                        src={place.images[0].url}
                        alt={place.images[0].alt}
                        className="w-full h-48 object-cover"
                      />
                    ) : (
                      <div className="w-full h-48 bg-gray-300 flex items-center justify-center">
                        <MapPinIcon className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* Place Info */}
                  <div className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900">{place.name}</h3>
                        <p className="text-sm text-gray-600 flex items-center mt-1">
                          <BuildingOfficeIcon className="h-4 w-4 mr-1" />
                          {place.city.name}, {place.city.country}
                        </p>
                      </div>
                      <Menu as="div" className="relative">
                        <Menu.Button className="flex items-center text-gray-400 hover:text-gray-600">
                          <EllipsisVerticalIcon className="h-5 w-5" />
                        </Menu.Button>
                        <Transition
                          as={Fragment}
                          enter="transition ease-out duration-100"
                          enterFrom="transform opacity-0 scale-95"
                          enterTo="transform opacity-100 scale-100"
                          leave="transition ease-in duration-75"
                          leaveFrom="transform opacity-100 scale-100"
                          leaveTo="transform opacity-0 scale-95"
                        >
                          <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                            <div className="py-1">
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    className={`${
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                                    } block w-full px-4 py-2 text-left text-sm`}
                                  >
                                    View Details
                                  </button>
                                )}
                              </Menu.Item>
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    className={`${
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                                    } block w-full px-4 py-2 text-left text-sm`}
                                  >
                                    Edit Place
                                  </button>
                                )}
                              </Menu.Item>
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => handleToggleStatus(place._id)}
                                    className={`${
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                                    } block w-full px-4 py-2 text-left text-sm`}
                                  >
                                    {place.isPublished ? 'Unpublish' : 'Publish'}
                                  </button>
                                )}
                              </Menu.Item>
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => handleToggleFeatured(place._id)}
                                    className={`${
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                                    } block w-full px-4 py-2 text-left text-sm`}
                                  >
                                    {place.isFeatured ? 'Unfeature' : 'Feature'}
                                  </button>
                                )}
                              </Menu.Item>
                              {admin?.role === 'super_admin' && (
                                <Menu.Item>
                                  {({ active }) => (
                                    <button
                                      className={`${
                                        active ? 'bg-gray-100 text-red-900' : 'text-red-700'
                                      } block w-full px-4 py-2 text-left text-sm`}
                                    >
                                      Delete Place
                                    </button>
                                  )}
                                </Menu.Item>
                              )}
                            </div>
                          </Menu.Items>
                        </Transition>
                      </Menu>
                    </div>

                    <p className="text-sm text-gray-600 mt-2 line-clamp-2">{place.description}</p>

                    {/* Category and Tags */}
                    <div className="flex items-center space-x-2 mt-3">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(place.category)}`}>
                        <TagIcon className="h-3 w-3 mr-1" />
                        {place.category}
                      </span>
                      {place.priceRange && (
                        <span className="text-xs text-gray-500">{place.priceRange}</span>
                      )}
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between mt-4">
                      <div className="flex items-center text-sm text-gray-500">
                        <StarIcon className="h-4 w-4 text-yellow-400 mr-1" />
                        {place.averageRating.toFixed(1)} ({place.totalReviews} reviews)
                      </div>
                      <div className="flex items-center space-x-2">
                        {place.isPublished ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <CheckCircleIcon className="h-3 w-3 mr-1" />
                            Published
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <XCircleIcon className="h-3 w-3 mr-1" />
                            Unpublished
                          </span>
                        )}
                        {place.isFeatured && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <StarIcon className="h-3 w-3 mr-1" />
                            Featured
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="text-xs text-gray-400 mt-2">
                      Created: {formatDate(place.createdAt)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
