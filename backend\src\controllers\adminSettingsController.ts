import { Response } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { AdminAuthRequest } from './adminAuthController';
import fs from 'fs';
import path from 'path';

interface AppSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  contactEmail: string;
  supportEmail: string;
  socialLinks: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  seo: {
    defaultTitle: string;
    defaultDescription: string;
    defaultKeywords: string[];
  };
  features: {
    userRegistration: boolean;
    reviewModeration: boolean;
    emailNotifications: boolean;
    socialLogin: boolean;
  };
  limits: {
    maxImageSize: number; // in MB
    maxImagesPerPlace: number;
    maxReviewLength: number;
    rateLimit: number; // requests per minute
  };
  email: {
    provider: 'smtp' | 'sendgrid' | 'mailgun';
    smtpHost?: string;
    smtpPort?: number;
    smtpUser?: string;
    smtpPassword?: string;
    fromEmail: string;
    fromName: string;
  };
  analytics: {
    googleAnalyticsId?: string;
    facebookPixelId?: string;
    hotjarId?: string;
  };
  maintenance: {
    enabled: boolean;
    message: string;
    allowedIPs: string[];
  };
}

const defaultSettings: AppSettings = {
  siteName: 'HeritEdge',
  siteDescription: 'Discover amazing places and create unforgettable travel experiences',
  siteUrl: 'https://heritedge.com',
  contactEmail: '<EMAIL>',
  supportEmail: '<EMAIL>',
  socialLinks: {
    facebook: '',
    twitter: '',
    instagram: '',
    linkedin: ''
  },
  seo: {
    defaultTitle: 'HeritEdge - Discover Amazing Places',
    defaultDescription: 'Explore cities, discover hidden gems, and plan your perfect trip with HeritEdge',
    defaultKeywords: ['travel', 'cities', 'places', 'tourism', 'heritage']
  },
  features: {
    userRegistration: true,
    reviewModeration: true,
    emailNotifications: true,
    socialLogin: true
  },
  limits: {
    maxImageSize: 5, // 5MB
    maxImagesPerPlace: 10,
    maxReviewLength: 1000,
    rateLimit: 100 // 100 requests per minute
  },
  email: {
    provider: 'smtp',
    fromEmail: '<EMAIL>',
    fromName: 'HeritEdge'
  },
  analytics: {
    googleAnalyticsId: '',
    facebookPixelId: '',
    hotjarId: ''
  },
  maintenance: {
    enabled: false,
    message: 'We are currently performing maintenance. Please check back soon.',
    allowedIPs: []
  }
};

const settingsFilePath = path.join(process.cwd(), 'config', 'app-settings.json');

// Ensure config directory exists
const configDir = path.dirname(settingsFilePath);
if (!fs.existsSync(configDir)) {
  fs.mkdirSync(configDir, { recursive: true });
}

// Load settings from file or create default
const loadSettings = (): AppSettings => {
  try {
    if (fs.existsSync(settingsFilePath)) {
      const data = fs.readFileSync(settingsFilePath, 'utf8');
      return { ...defaultSettings, ...JSON.parse(data) };
    }
  } catch (error) {
    console.error('Error loading settings:', error);
  }
  return defaultSettings;
};

// Save settings to file
const saveSettings = (settings: AppSettings): void => {
  try {
    fs.writeFileSync(settingsFilePath, JSON.stringify(settings, null, 2));
  } catch (error) {
    console.error('Error saving settings:', error);
    throw new Error('Failed to save settings');
  }
};

// @desc    Get application settings
// @route   GET /api/admin/settings
// @access  Private (Admin)
export const getSettings = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  try {
    const settings = loadSettings();
    
    // Remove sensitive information from response
    const publicSettings = { ...settings };
    if (publicSettings.email.smtpPassword) {
      publicSettings.email.smtpPassword = '***';
    }

    res.json({
      success: true,
      data: publicSettings
    });
  } catch (error) {
    console.error('Error getting settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get settings'
    });
  }
});

// @desc    Update application settings
// @route   PUT /api/admin/settings
// @access  Private (Super Admin)
export const updateSettings = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  try {
    const currentSettings = loadSettings();
    const updatedSettings = { ...currentSettings, ...req.body };

    // Validate required fields
    if (!updatedSettings.siteName || !updatedSettings.contactEmail) {
      return res.status(400).json({
        success: false,
        error: 'Site name and contact email are required'
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(updatedSettings.contactEmail) || !emailRegex.test(updatedSettings.supportEmail)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid email format'
      });
    }

    saveSettings(updatedSettings);

    // Remove sensitive information from response
    const publicSettings = { ...updatedSettings };
    if (publicSettings.email.smtpPassword) {
      publicSettings.email.smtpPassword = '***';
    }

    res.json({
      success: true,
      message: 'Settings updated successfully',
      data: publicSettings
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update settings'
    });
  }
});

// @desc    Get system information
// @route   GET /api/admin/settings/system
// @access  Private (Admin)
export const getSystemInfo = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  try {
    const systemInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      architecture: process.arch,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      environment: process.env.NODE_ENV || 'development',
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      data: systemInfo
    });
  } catch (error) {
    console.error('Error getting system info:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get system information'
    });
  }
});

// @desc    Test email configuration
// @route   POST /api/admin/settings/test-email
// @access  Private (Admin)
export const testEmailConfig = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      error: 'Email address is required'
    });
  }

  try {
    // This is a placeholder - in a real implementation, you would
    // use the configured email service to send a test email
    console.log(`Test email would be sent to: ${email}`);

    res.json({
      success: true,
      message: 'Test email sent successfully'
    });
  } catch (error) {
    console.error('Error sending test email:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send test email'
    });
  }
});

// @desc    Clear application cache
// @route   POST /api/admin/settings/clear-cache
// @access  Private (Admin)
export const clearCache = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  try {
    // This is a placeholder - in a real implementation, you would
    // clear Redis cache, file cache, etc.
    console.log('Cache cleared by admin:', req.admin?.email);

    res.json({
      success: true,
      message: 'Cache cleared successfully'
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear cache'
    });
  }
});

// @desc    Export application data
// @route   GET /api/admin/settings/export
// @access  Private (Super Admin)
export const exportData = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  try {
    // This is a placeholder - in a real implementation, you would
    // export data from the database
    const exportData = {
      timestamp: new Date().toISOString(),
      exportedBy: req.admin?.email,
      message: 'Data export functionality would be implemented here'
    };

    res.json({
      success: true,
      data: exportData
    });
  } catch (error) {
    console.error('Error exporting data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to export data'
    });
  }
});

// @desc    Get backup status
// @route   GET /api/admin/settings/backup-status
// @access  Private (Admin)
export const getBackupStatus = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  try {
    // This is a placeholder - in a real implementation, you would
    // check the status of automated backups
    const backupStatus = {
      lastBackup: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 24 hours ago
      nextBackup: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
      backupSize: '2.5 GB',
      status: 'healthy',
      autoBackupEnabled: true,
      retentionDays: 30
    };

    res.json({
      success: true,
      data: backupStatus
    });
  } catch (error) {
    console.error('Error getting backup status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get backup status'
    });
  }
});
