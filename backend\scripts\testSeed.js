const mongoose = require('mongoose');

// Simple test to add one city
async function testSeed() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect('mongodb://localhost:27017/citytales');
    console.log('✅ Connected successfully');

    // Simple city schema
    const citySchema = new mongoose.Schema({
      name: String,
      slug: String,
      country: String,
      description: String,
      isPublished: { type: Boolean, default: true },
      isFeatured: { type: Boolean, default: false }
    }, { timestamps: true });

    const City = mongoose.model('City', citySchema);

    // Add one test city
    const testCity = new City({
      name: 'Paris',
      slug: 'paris',
      country: 'France',
      description: 'The City of Light',
      isPublished: true,
      isFeatured: true
    });

    await testCity.save();
    console.log('✅ Test city added successfully');

    const count = await City.countDocuments();
    console.log(`📊 Total cities in database: ${count}`);

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected');
  }
}

testSeed();
