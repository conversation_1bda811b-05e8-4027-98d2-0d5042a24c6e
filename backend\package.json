{"name": "heritedge-backend", "version": "1.0.0", "description": "Backend API for HeritEdge - A platform for showcasing city information", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "create-admin": "npx ts-node src/scripts/seedAdmin.ts", "seed-admin": "npx ts-node src/scripts/seedAdmin.ts", "test-models": "npx ts-node src/scripts/testModels.ts", "test-endpoint": "npx ts-node src/scripts/testEndpoint.ts"}, "keywords": ["citytales", "travel", "cities", "api", "express"], "author": "CityTales Team", "license": "MIT", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.3"}}