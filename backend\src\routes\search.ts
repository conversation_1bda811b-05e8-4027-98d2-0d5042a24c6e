import express from 'express';
import { query } from 'express-validator';
import {
  globalSearch,
  searchCities,
  searchPlaces,
  searchItineraries,
  getSearchSuggestions,
  getTrendingSearches
} from '../controllers/searchController';

const router = express.Router();

// @route   GET /api/search
// @desc    Global search across cities and places
// @access  Public
router.get('/', [
  query('q')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Search query must be between 2 and 100 characters'),
  query('type')
    .optional()
    .isIn(['all', 'cities', 'places'])
    .withMessage('Type must be all, cities, or places'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  query('country')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('Country must be at least 2 characters'),
  query('category')
    .optional()
    .isIn(['historical', 'attraction', 'restaurant', 'hotel', 'shopping', 'entertainment', 'nature', 'religious', 'museum', 'other'])
    .withMessage('Invalid category')
], globalSearch);

// @route   GET /api/search/cities
// @desc    Search cities specifically
// @access  Public
router.get('/cities', [
  query('q')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Search query must be between 2 and 100 characters'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  query('country')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('Country must be at least 2 characters'),
  query('minRating')
    .optional()
    .isFloat({ min: 0, max: 5 })
    .withMessage('Minimum rating must be between 0 and 5')
], searchCities);

// @route   GET /api/search/places
// @desc    Search places specifically
// @access  Public
router.get('/places', [
  query('q')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Search query must be between 2 and 100 characters'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  query('city')
    .optional()
    .isMongoId()
    .withMessage('Invalid city ID'),
  query('category')
    .optional()
    .isIn(['historical', 'attraction', 'restaurant', 'hotel', 'shopping', 'entertainment', 'nature', 'religious', 'museum', 'other'])
    .withMessage('Invalid category'),
  query('minRating')
    .optional()
    .isFloat({ min: 0, max: 5 })
    .withMessage('Minimum rating must be between 0 and 5'),
  query('priceRange')
    .optional()
    .isIn(['budget', 'moderate', 'expensive', 'luxury'])
    .withMessage('Invalid price range')
], searchPlaces);

// @route   GET /api/search/itineraries
// @desc    Search itineraries specifically
// @access  Public
router.get('/itineraries', [
  query('q')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Search query must be between 2 and 100 characters'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
], searchItineraries);

// @route   GET /api/search/suggestions
// @desc    Get search suggestions/autocomplete
// @access  Public
router.get('/suggestions', [
  query('q')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Search query must be between 1 and 50 characters'),
  query('type')
    .optional()
    .isIn(['all', 'cities', 'places'])
    .withMessage('Type must be all, cities, or places'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('Limit must be between 1 and 20')
], getSearchSuggestions);

// @route   GET /api/search/trending
// @desc    Get trending searches
// @access  Public
router.get('/trending', [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('Limit must be between 1 and 20')
], getTrendingSearches);

export default router;
