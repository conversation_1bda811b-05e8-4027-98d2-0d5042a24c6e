import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Import models to ensure they're registered
import { User, Admin, City, Place, Review, Itinerary } from '../models';

const testModels = async () => {
  try {
    // Connect to MongoDB
    const mongoURI = process.env.MONGODB_URI;
    if (!mongoURI) {
      throw new Error('MONGODB_URI is not defined in environment variables');
    }

    await mongoose.connect(mongoURI);
    console.log('📊 Connected to MongoDB');

    // Test model registration
    const modelNames = mongoose.modelNames();
    console.log('📋 Registered models:', modelNames);

    // Test each model directly
    const models = [
      { name: 'User', model: User },
      { name: 'Admin', model: Admin },
      { name: 'City', model: City },
      { name: 'Place', model: Place },
      { name: 'Review', model: Review },
      { name: 'Itinerary', model: Itinerary }
    ];

    for (const { name, model } of models) {
      try {
        const count = await model.countDocuments();
        console.log(`✅ ${name}: ${count} documents`);
      } catch (error) {
        console.error(`❌ ${name}: Error -`, error);
      }
    }

    // Test the specific query that was failing
    console.log('\n🧪 Testing dashboard activity query...');
    try {
      const recentCities = await City.find({ isPublished: true })
        .sort({ createdAt: -1 })
        .limit(3)
        .select('name country createdAt');
      console.log('✅ Dashboard activity query successful:', recentCities.length, 'cities found');
    } catch (error) {
      console.error('❌ Dashboard activity query failed:', error);
    }

  } catch (error) {
    console.error('❌ Error testing models:', error);
  } finally {
    await mongoose.connection.close();
    console.log('📊 MongoDB connection closed');
    process.exit(0);
  }
};

// Run the test
testModels();
