import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Import models to ensure they're registered
import '../models';

const testModels = async () => {
  try {
    // Connect to MongoDB
    const mongoURI = process.env.MONGODB_URI;
    if (!mongoURI) {
      throw new Error('MONGODB_URI is not defined in environment variables');
    }

    await mongoose.connect(mongoURI);
    console.log('📊 Connected to MongoDB');

    // Test model registration
    const modelNames = mongoose.modelNames();
    console.log('📋 Registered models:', modelNames);

    // Test each model
    const models = ['User', 'Admin', 'City', 'Place', 'Review', 'Itinerary'];
    
    for (const modelName of models) {
      try {
        const Model = mongoose.model(modelName);
        const count = await Model.countDocuments();
        console.log(`✅ ${modelName}: ${count} documents`);
      } catch (error) {
        console.error(`❌ ${modelName}: Error -`, error);
      }
    }

  } catch (error) {
    console.error('❌ Error testing models:', error);
  } finally {
    await mongoose.connection.close();
    console.log('📊 MongoDB connection closed');
    process.exit(0);
  }
};

// Run the test
testModels();
