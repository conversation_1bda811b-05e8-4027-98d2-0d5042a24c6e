import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/providers";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { Toaster } from "react-hot-toast";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: {
    default: "HeritEdge - Discover Amazing Cities Around the World",
    template: "%s | HeritEdge"
  },
  description: "Explore cities, discover hidden gems, and plan your perfect trip with HeritEdge. Find the best attractions, restaurants, and local experiences worldwide.",
  keywords: ["travel", "cities", "tourism", "attractions", "restaurants", "hotels", "travel guide"],
  authors: [{ name: "HeritEdge Team" }],
  creator: "HeritEdge",
  publisher: "HeritEdge",
  metadataBase: new URL(process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000'),
  openGraph: {
    type: "website",
    locale: "en_US",
    url: process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000',
    siteName: "HeritEdge",
    title: "HeritEdge - Discover Amazing Cities Around the World",
    description: "Explore cities, discover hidden gems, and plan your perfect trip with HeritEdge.",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "HeritEdge - Discover Amazing Cities",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "HeritEdge - Discover Amazing Cities Around the World",
    description: "Explore cities, discover hidden gems, and plan your perfect trip with HeritEdge.",
    images: ["/og-image.jpg"],
    creator: "@heritedge",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="min-h-screen bg-gray-50 font-sans antialiased">
        <Providers>
          <div className="flex min-h-screen flex-col">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}
