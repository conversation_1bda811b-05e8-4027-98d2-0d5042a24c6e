import { Request, Response } from 'express';
import Video from '../models/Video';
import Place from '../models/Place';
import { AuthRequest } from '../middleware/auth';
import { AdminAuthRequest } from './adminAuthController';
import mongoose from 'mongoose';

// Submit a new video for approval
export const submitVideo = async (req: AuthRequest, res: Response) => {
  try {
    const { title, description, youtubeUrl, placeId, tags } = req.body;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Validate place exists
    const place = await Place.findById(placeId);
    if (!place) {
      return res.status(404).json({
        success: false,
        error: 'Place not found'
      });
    }

    // Extract YouTube video ID
    const youtubeRegex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
    const match = youtubeUrl.match(youtubeRegex);
    
    if (!match) {
      return res.status(400).json({
        success: false,
        error: 'Invalid YouTube URL format'
      });
    }

    const youtubeVideoId = match[1];

    // Check if video already exists for this place
    const existingVideo = await Video.findOne({ 
      place: placeId, 
      youtubeVideoId 
    });

    if (existingVideo) {
      return res.status(409).json({
        success: false,
        error: 'This video has already been submitted for this place'
      });
    }

    const video = new Video({
      title,
      description,
      youtubeUrl,
      youtubeVideoId,
      place: placeId,
      submittedBy: userId,
      tags: tags || [],
      status: 'pending'
    });

    await video.save();

    // Populate the response
    await video.populate([
      { path: 'place', select: 'name slug' },
      { path: 'submittedBy', select: 'name email' }
    ]);

    res.status(201).json({
      success: true,
      data: { video },
      message: 'Video submitted successfully and is pending approval'
    });

  } catch (error: any) {
    console.error('Submit video error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to submit video'
    });
  }
};

// Get approved videos for a place
export const getPlaceVideos = async (req: Request, res: Response) => {
  try {
    const { placeId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const videos = await Video.find({
      place: placeId,
      status: 'approved',
      isActive: true
    })
    .populate('submittedBy', 'name')
    .sort({ reviewDate: -1, submissionDate: -1 })
    .limit(Number(limit) * 1)
    .skip((Number(page) - 1) * Number(limit));

    const total = await Video.countDocuments({
      place: placeId,
      status: 'approved',
      isActive: true
    });

    res.json({
      success: true,
      data: {
        videos,
        pagination: {
          currentPage: Number(page),
          totalPages: Math.ceil(total / Number(limit)),
          totalItems: total,
          itemsPerPage: Number(limit)
        }
      }
    });

  } catch (error: any) {
    console.error('Get place videos error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to fetch videos'
    });
  }
};

// Get user's submitted videos
export const getUserVideos = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { page = 1, limit = 10, status } = req.query;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const filter: any = { submittedBy: userId };
    if (status) {
      filter.status = status;
    }

    const videos = await Video.find(filter)
      .populate('place', 'name slug')
      .sort({ submissionDate: -1 })
      .limit(Number(limit) * 1)
      .skip((Number(page) - 1) * Number(limit));

    const total = await Video.countDocuments(filter);

    res.json({
      success: true,
      data: {
        videos,
        pagination: {
          currentPage: Number(page),
          totalPages: Math.ceil(total / Number(limit)),
          totalItems: total,
          itemsPerPage: Number(limit)
        }
      }
    });

  } catch (error: any) {
    console.error('Get user videos error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to fetch user videos'
    });
  }
};

// Get pending videos for admin review
export const getPendingVideos = async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 20 } = req.query;

    const videos = await Video.find({ status: 'pending' })
      .populate('place', 'name slug')
      .populate('submittedBy', 'name email')
      .sort({ submissionDate: -1 })
      .limit(Number(limit) * 1)
      .skip((Number(page) - 1) * Number(limit));

    const total = await Video.countDocuments({ status: 'pending' });

    res.json({
      success: true,
      data: {
        videos,
        pagination: {
          currentPage: Number(page),
          totalPages: Math.ceil(total / Number(limit)),
          totalItems: total,
          itemsPerPage: Number(limit)
        }
      }
    });

  } catch (error: any) {
    console.error('Get pending videos error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to fetch pending videos'
    });
  }
};

// Approve or reject a video (Admin only)
export const reviewVideo = async (req: AdminAuthRequest, res: Response) => {
  try {
    const { videoId } = req.params;
    const { status, reviewNotes } = req.body;
    const reviewerId = req.admin?._id;

    if (!reviewerId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Status must be either "approved" or "rejected"'
      });
    }

    const video = await Video.findById(videoId);
    if (!video) {
      return res.status(404).json({
        success: false,
        error: 'Video not found'
      });
    }

    if (video.status !== 'pending') {
      return res.status(400).json({
        success: false,
        error: 'Video has already been reviewed'
      });
    }

    video.status = status;
    video.reviewedBy = new mongoose.Types.ObjectId(reviewerId);
    video.reviewDate = new Date();
    video.reviewNotes = reviewNotes;

    await video.save();

    await video.populate([
      { path: 'place', select: 'name slug' },
      { path: 'submittedBy', select: 'name email' },
      { path: 'reviewedBy', select: 'name email' }
    ]);

    res.json({
      success: true,
      data: { video },
      message: `Video ${status} successfully`
    });

  } catch (error: any) {
    console.error('Review video error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to review video'
    });
  }
};

// Delete a video (Admin or original submitter)
export const deleteVideo = async (req: AuthRequest, res: Response) => {
  try {
    const { videoId } = req.params;
    const userId = req.user?.id;
    const isAdmin = req.user?.role === 'admin';

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const video = await Video.findById(videoId);
    if (!video) {
      return res.status(404).json({
        success: false,
        error: 'Video not found'
      });
    }

    // Check permissions
    if (!isAdmin && video.submittedBy.toString() !== userId) {
      return res.status(403).json({
        success: false,
        error: 'You can only delete your own videos'
      });
    }

    await Video.findByIdAndDelete(videoId);

    res.json({
      success: true,
      message: 'Video deleted successfully'
    });

  } catch (error: any) {
    console.error('Delete video error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to delete video'
    });
  }
};

// Increment view count
export const incrementViewCount = async (req: Request, res: Response) => {
  try {
    const { videoId } = req.params;

    await Video.findByIdAndUpdate(
      videoId,
      { $inc: { viewCount: 1 } },
      { new: true }
    );

    res.json({
      success: true,
      message: 'View count updated'
    });

  } catch (error: any) {
    console.error('Increment view count error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update view count'
    });
  }
};
