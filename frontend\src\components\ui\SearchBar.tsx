'use client';

import { useState, useEffect, useRef } from 'react';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';

interface SearchBarProps {
  isOpen: boolean;
  onClose: () => void;
}

export function SearchBar({ isOpen, onClose }: SearchBarProps) {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Available cities to avoid API rate limits
  const availableCities = [
    { name: 'Paris', slug: 'paris' },
    { name: 'London', slug: 'london' },
    { name: 'Rome', slug: 'rome' },
    { name: 'Tokyo', slug: 'tokyo' },
    { name: 'New York', slug: 'new-york' },
    { name: 'Barcelona', slug: 'barcelona' },
    { name: 'Amsterdam', slug: 'amsterdam' },
    { name: 'Prague', slug: 'prague' },
    { name: 'Istanbul', slug: 'istanbul' },
    { name: 'Cairo', slug: 'cairo' },
    { name: 'Athens', slug: 'athens' },
    { name: 'Florence', slug: 'florence' }
  ];

  const handleSearch = (searchQuery: string) => {
    if (searchQuery.trim()) {
      // Check if it's one of our available cities
      const city = availableCities.find(c =>
        c.name.toLowerCase() === searchQuery.trim().toLowerCase()
      );

      if (city) {
        // Navigate directly to the city page
        router.push(`/cities/${city.slug}`);
      } else {
        // For other searches, still use the search page but with limited functionality
        router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      }
      onClose();
    }
  };

  const handleCityClick = (citySlug: string) => {
    router.push(`/cities/${citySlug}`);
    onClose();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch(query);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm flex items-start justify-center pt-20 px-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-3xl transform transition-all duration-300 scale-100 opacity-100">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl">
            <MagnifyingGlassIcon className="h-6 w-6 text-blue-600" />
            <input
              ref={inputRef}
              type="text"
              placeholder="Search cities, places, attractions..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="flex-1 text-lg bg-transparent border-none outline-none placeholder-gray-500 text-gray-900"
            />
            <button
              type="button"
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-200 rounded-lg transition-all duration-200"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </form>

        {/* Enhanced Search Results */}
        {query && (
          <div className="px-6 pb-6">
            <div className="text-sm text-gray-500 mb-4 flex items-center space-x-2">
              <span>Press Enter to search for</span>
              <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-md font-medium">&quot;{query}&quot;</span>
            </div>
            <div className="space-y-2">
              <button
                onClick={() => handleSearch(query)}
                className="w-full text-left p-3 hover:bg-blue-50 rounded-xl transition-all duration-200 border border-transparent hover:border-blue-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <MagnifyingGlassIcon className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">Search for &quot;{query}&quot;</div>
                    <div className="text-sm text-gray-500">Find cities, places, and attractions</div>
                  </div>
                </div>
              </button>
            </div>
          </div>
        )}

        {!query && (
          <div className="px-6 pb-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <div className="text-xs font-medium text-blue-800 mb-1">Search Notice</div>
              <div className="text-xs text-blue-700">
                Search is limited to our {availableCities.length} curated heritage cities. Click a destination below or type to search.
              </div>
            </div>
            <div className="text-sm font-medium text-gray-700 mb-4">🔥 Available destinations</div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {[
                { name: 'Paris', emoji: '🇫🇷', description: 'City of Light', slug: 'paris' },
                { name: 'London', emoji: '🇬🇧', description: 'Historic capital', slug: 'london' },
                { name: 'Rome', emoji: '🇮🇹', description: 'Eternal City', slug: 'rome' },
                { name: 'Tokyo', emoji: '🇯🇵', description: 'Modern metropolis', slug: 'tokyo' },
                { name: 'New York', emoji: '🇺🇸', description: 'The Big Apple', slug: 'new-york' },
                { name: 'Barcelona', emoji: '🇪🇸', description: 'Mediterranean gem', slug: 'barcelona' },
                { name: 'Amsterdam', emoji: '🇳🇱', description: 'Venice of the North', slug: 'amsterdam' },
                { name: 'Prague', emoji: '🇨🇿', description: 'City of a Hundred Spires', slug: 'prague' },
                { name: 'Istanbul', emoji: '🇹🇷', description: 'Bridge Between Continents', slug: 'istanbul' },
                { name: 'Cairo', emoji: '🇪🇬', description: 'Gateway to Ancient Egypt', slug: 'cairo' },
                { name: 'Athens', emoji: '🇬🇷', description: 'Cradle of Democracy', slug: 'athens' },
                { name: 'Florence', emoji: '🇮🇹', description: 'Renaissance Jewel', slug: 'florence' }
              ].map((city) => (
                <button
                  key={city.name}
                  onClick={() => handleCityClick(city.slug)}
                  className="text-left p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 border border-transparent hover:border-gray-200"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{city.emoji}</span>
                    <div>
                      <div className="font-medium text-gray-900">{city.name}</div>
                      <div className="text-sm text-gray-500">{city.description}</div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
